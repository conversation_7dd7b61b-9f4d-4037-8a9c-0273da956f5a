{"version": 3, "file": "866.8574f33a07edc3fc33b5.js?v=8574f33a07edc3fc33b5", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,oBAAoB;AAC7C;AACA,CAAC;;AAEM;AACP;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kBAAkB,sBAAsB;AACxC;AACA;AACA;AACA;;AAEA;;AAEO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,mEAAmE,eAAe;AAClF;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,4BAA4B;AAC5B,6BAA6B;AAC7B,+BAA+B;;;ACtFqD;;AAEpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,KAAK,wBAAwB,YAAY;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwD,GAAG,KAAK,WAAW;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB,qBAAqB;AACrB;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,mCAAmC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uCAAuC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,0BAA0B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,uCAAuC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,8BAA8B;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,yBAAyB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA,oDAAoD;AACpD;AACA,8DAA8D;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAgB;AACzB,WAAW,gBAAkB;AAC7B;AACA,SAAS,iBAAY,OAAO;AAC5B,SAAS,kBAAa,OAAO;AAC7B;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAW;AACpB;AACA,SAAS,kBAAa;AACtB;AACA;AACA,SAAS,iBAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAa,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0BAA0B;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,yBAAyB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,KAAK,0CAA0C,KAAK;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,sCAAsC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,qBAAqB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,YAAY,aAAa,OAAO;AAC9G;AACA;AACA;AACA;AACA,sBAAsB,0BAA0B;AAChD;AACA,iEAAiE,MAAM,KAAK,IAAI,oBAAoB,OAAO;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,yBAAyB;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,WAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,iBAAiB;AACjB;AACA,cAAc,WAAW;AACzB;AACA;AACA;AACA,cAAc,WAAW;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,wBAAwB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,mBAAmB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,qBAAqB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,4CAA4C,uCAAuC;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,IAAI;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE;AACvE;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,QAAQ;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,QAAQ;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,oCAAoC;AACxD;AACA;AACA;AACA,6CAA6C,WAAW,IAAI,OAAO;AACnE;AACA;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,qCAAqC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,qBAAqB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,WAAW,8CAA8C;AACvG,qEAAqE;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA,4BAA4B,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,qBAAqB;AACnC;AACA;AACA;AACA,uBAAuB,qBAAgB;AACvC;AACA;AACA;AACA;AACA;AACA,uBAAuB,qBAAgB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,iEAAiE;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oIAAoI,OAAO;AAC3I;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,iEAAiE;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,0CAA0C;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,cAAc;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,QAAQ;AAC9C,sCAAsC,yBAAyB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,mBAAmB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC,oCAAoC,cAAc;AAClD;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,kBAAkB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,+BAA+B;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,QAAQ;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,iCAAiC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA,8CAA8C,OAAO;AACrD;AACA;AACA;AACA;AACA,mCAAmC,YAAY;AAC/C;AACA;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,4BAA4B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,qBAAgB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA,YAAY,qBAAgB;AAC5B;AACA;AACA;;AAEkX", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@marijn/find-cluster-break/src/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/state/dist/index.js"], "sourcesContent": ["// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [], rangeTo = []\n\n;(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1)\n  for (let i = 0, n = 0; i < numbers.length; i++)\n    (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i])\n})()\n\nexport function isExtendingChar(code) {\n  if (code < 768) return false\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = (from + to) >> 1\n    if (code < rangeFrom[mid]) to = mid\n    else if (code >= rangeTo[mid]) from = mid + 1\n    else return true\n    if (from == to) return false\n  }\n}\n\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF\n}\n\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code\n  }\n  return false\n}\n\nconst ZWJ = 0x200d\n\nexport function findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending)\n}\n\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--\n  let prev = codePointAt(str, pos)\n  pos += codePointSize(prev)\n  while (pos < str.length) {\n    let next = codePointAt(str, pos)\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next)\n      prev = next\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0, i = pos - 2\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) { countBefore++; i -= 2 }\n      if (countBefore % 2 == 0) break\n      else pos += 2\n    } else {\n      break\n    }\n  }\n  return pos\n}\n\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending)\n    if (found < pos) return found\n    pos--\n  }\n  return 0\n}\n\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos)\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0\n  let code1 = str.charCodeAt(pos + 1)\n  if (!surrogateLow(code1)) return code0\n  return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000\n}\n\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000 }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00 }\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2 }\n", "import { find<PERSON>lusterBreak as findClusterBreak$1 } from '@marijn/find-cluster-break';\n\n/**\nThe data structure for documents. @nonabstract\n*/\nclass Text {\n    /**\n    Get the line description around the given position.\n    */\n    lineAt(pos) {\n        if (pos < 0 || pos > this.length)\n            throw new RangeError(`Invalid position ${pos} in document of length ${this.length}`);\n        return this.lineInner(pos, false, 1, 0);\n    }\n    /**\n    Get the description for the given (1-based) line number.\n    */\n    line(n) {\n        if (n < 1 || n > this.lines)\n            throw new RangeError(`Invalid line number ${n} in ${this.lines}-line document`);\n        return this.lineInner(n, true, 1, 0);\n    }\n    /**\n    Replace a range of the text with the given content.\n    */\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(0, from, parts, 2 /* Open.To */);\n        if (text.length)\n            text.decompose(0, text.length, parts, 1 /* Open.From */ | 2 /* Open.To */);\n        this.decompose(to, this.length, parts, 1 /* Open.From */);\n        return TextNode.from(parts, this.length - (to - from) + text.length);\n    }\n    /**\n    Append another document to this one.\n    */\n    append(other) {\n        return this.replace(this.length, this.length, other);\n    }\n    /**\n    Retrieve the text between the given points.\n    */\n    slice(from, to = this.length) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(from, to, parts, 0);\n        return TextNode.from(parts, to - from);\n    }\n    /**\n    Test whether this text is equal to another instance.\n    */\n    eq(other) {\n        if (other == this)\n            return true;\n        if (other.length != this.length || other.lines != this.lines)\n            return false;\n        let start = this.scanIdentical(other, 1), end = this.length - this.scanIdentical(other, -1);\n        let a = new RawTextCursor(this), b = new RawTextCursor(other);\n        for (let skip = start, pos = start;;) {\n            a.next(skip);\n            b.next(skip);\n            skip = 0;\n            if (a.lineBreak != b.lineBreak || a.done != b.done || a.value != b.value)\n                return false;\n            pos += a.value.length;\n            if (a.done || pos >= end)\n                return true;\n        }\n    }\n    /**\n    Iterate over the text. When `dir` is `-1`, iteration happens\n    from end to start. This will return lines and the breaks between\n    them as separate strings.\n    */\n    iter(dir = 1) { return new RawTextCursor(this, dir); }\n    /**\n    Iterate over a range of the text. When `from` > `to`, the\n    iterator will run in reverse.\n    */\n    iterRange(from, to = this.length) { return new PartialTextCursor(this, from, to); }\n    /**\n    Return a cursor that iterates over the given range of lines,\n    _without_ returning the line breaks between, and yielding empty\n    strings for empty lines.\n    \n    When `from` and `to` are given, they should be 1-based line numbers.\n    */\n    iterLines(from, to) {\n        let inner;\n        if (from == null) {\n            inner = this.iter();\n        }\n        else {\n            if (to == null)\n                to = this.lines + 1;\n            let start = this.line(from).from;\n            inner = this.iterRange(start, Math.max(start, to == this.lines + 1 ? this.length : to <= 1 ? 0 : this.line(to - 1).to));\n        }\n        return new LineCursor(inner);\n    }\n    /**\n    Return the document as a string, using newline characters to\n    separate lines.\n    */\n    toString() { return this.sliceString(0); }\n    /**\n    Convert the document to an array of lines (which can be\n    deserialized again via [`Text.of`](https://codemirror.net/6/docs/ref/#state.Text^of)).\n    */\n    toJSON() {\n        let lines = [];\n        this.flatten(lines);\n        return lines;\n    }\n    /**\n    @internal\n    */\n    constructor() { }\n    /**\n    Create a `Text` instance for the given array of lines.\n    */\n    static of(text) {\n        if (text.length == 0)\n            throw new RangeError(\"A document must have at least one line\");\n        if (text.length == 1 && !text[0])\n            return Text.empty;\n        return text.length <= 32 /* Tree.Branch */ ? new TextLeaf(text) : TextNode.from(TextLeaf.split(text, []));\n    }\n}\n// Leaves store an array of line strings. There are always line breaks\n// between these strings. Leaves are limited in size and have to be\n// contained in TextNode instances for bigger documents.\nclass TextLeaf extends Text {\n    constructor(text, length = textLength(text)) {\n        super();\n        this.text = text;\n        this.length = length;\n    }\n    get lines() { return this.text.length; }\n    get children() { return null; }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let string = this.text[i], end = offset + string.length;\n            if ((isLine ? line : end) >= target)\n                return new Line(offset, end, line, string);\n            offset = end + 1;\n            line++;\n        }\n    }\n    decompose(from, to, target, open) {\n        let text = from <= 0 && to >= this.length ? this\n            : new TextLeaf(sliceText(this.text, from, to), Math.min(to, this.length) - Math.max(0, from));\n        if (open & 1 /* Open.From */) {\n            let prev = target.pop();\n            let joined = appendText(text.text, prev.text.slice(), 0, text.length);\n            if (joined.length <= 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(joined, prev.length + text.length));\n            }\n            else {\n                let mid = joined.length >> 1;\n                target.push(new TextLeaf(joined.slice(0, mid)), new TextLeaf(joined.slice(mid)));\n            }\n        }\n        else {\n            target.push(text);\n        }\n    }\n    replace(from, to, text) {\n        if (!(text instanceof TextLeaf))\n            return super.replace(from, to, text);\n        [from, to] = clip(this, from, to);\n        let lines = appendText(this.text, appendText(text.text, sliceText(this.text, 0, from)), to);\n        let newLen = this.length + text.length - (to - from);\n        if (lines.length <= 32 /* Tree.Branch */)\n            return new TextLeaf(lines, newLen);\n        return TextNode.from(TextLeaf.split(lines, []), newLen);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let pos = 0, i = 0; pos <= to && i < this.text.length; i++) {\n            let line = this.text[i], end = pos + line.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += line.slice(Math.max(0, from - pos), to - pos);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let line of this.text)\n            target.push(line);\n    }\n    scanIdentical() { return 0; }\n    static split(text, target) {\n        let part = [], len = -1;\n        for (let line of text) {\n            part.push(line);\n            len += line.length + 1;\n            if (part.length == 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(part, len));\n                part = [];\n                len = -1;\n            }\n        }\n        if (len > -1)\n            target.push(new TextLeaf(part, len));\n        return target;\n    }\n}\n// Nodes provide the tree structure of the `Text` type. They store a\n// number of other nodes or leaves, taking care to balance themselves\n// on changes. There are implied line breaks _between_ the children of\n// a node (but not before the first or after the last child).\nclass TextNode extends Text {\n    constructor(children, length) {\n        super();\n        this.children = children;\n        this.length = length;\n        this.lines = 0;\n        for (let child of children)\n            this.lines += child.lines;\n    }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let child = this.children[i], end = offset + child.length, endLine = line + child.lines - 1;\n            if ((isLine ? endLine : end) >= target)\n                return child.lineInner(target, isLine, line, offset);\n            offset = end + 1;\n            line = endLine + 1;\n        }\n    }\n    decompose(from, to, target, open) {\n        for (let i = 0, pos = 0; pos <= to && i < this.children.length; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (from <= end && to >= pos) {\n                let childOpen = open & ((pos <= from ? 1 /* Open.From */ : 0) | (end >= to ? 2 /* Open.To */ : 0));\n                if (pos >= from && end <= to && !childOpen)\n                    target.push(child);\n                else\n                    child.decompose(from - pos, to - pos, target, childOpen);\n            }\n            pos = end + 1;\n        }\n    }\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        if (text.lines < this.lines)\n            for (let i = 0, pos = 0; i < this.children.length; i++) {\n                let child = this.children[i], end = pos + child.length;\n                // Fast path: if the change only affects one child and the\n                // child's size remains in the acceptable range, only update\n                // that child\n                if (from >= pos && to <= end) {\n                    let updated = child.replace(from - pos, to - pos, text);\n                    let totalLines = this.lines - child.lines + updated.lines;\n                    if (updated.lines < (totalLines >> (5 /* Tree.BranchShift */ - 1)) &&\n                        updated.lines > (totalLines >> (5 /* Tree.BranchShift */ + 1))) {\n                        let copy = this.children.slice();\n                        copy[i] = updated;\n                        return new TextNode(copy, this.length - (to - from) + text.length);\n                    }\n                    return super.replace(pos, end, updated);\n                }\n                pos = end + 1;\n            }\n        return super.replace(from, to, text);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let i = 0, pos = 0; i < this.children.length && pos <= to; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += child.sliceString(from - pos, to - pos, lineSep);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let child of this.children)\n            child.flatten(target);\n    }\n    scanIdentical(other, dir) {\n        if (!(other instanceof TextNode))\n            return 0;\n        let length = 0;\n        let [iA, iB, eA, eB] = dir > 0 ? [0, 0, this.children.length, other.children.length]\n            : [this.children.length - 1, other.children.length - 1, -1, -1];\n        for (;; iA += dir, iB += dir) {\n            if (iA == eA || iB == eB)\n                return length;\n            let chA = this.children[iA], chB = other.children[iB];\n            if (chA != chB)\n                return length + chA.scanIdentical(chB, dir);\n            length += chA.length + 1;\n        }\n    }\n    static from(children, length = children.reduce((l, ch) => l + ch.length + 1, -1)) {\n        let lines = 0;\n        for (let ch of children)\n            lines += ch.lines;\n        if (lines < 32 /* Tree.Branch */) {\n            let flat = [];\n            for (let ch of children)\n                ch.flatten(flat);\n            return new TextLeaf(flat, length);\n        }\n        let chunk = Math.max(32 /* Tree.Branch */, lines >> 5 /* Tree.BranchShift */), maxChunk = chunk << 1, minChunk = chunk >> 1;\n        let chunked = [], currentLines = 0, currentLen = -1, currentChunk = [];\n        function add(child) {\n            let last;\n            if (child.lines > maxChunk && child instanceof TextNode) {\n                for (let node of child.children)\n                    add(node);\n            }\n            else if (child.lines > minChunk && (currentLines > minChunk || !currentLines)) {\n                flush();\n                chunked.push(child);\n            }\n            else if (child instanceof TextLeaf && currentLines &&\n                (last = currentChunk[currentChunk.length - 1]) instanceof TextLeaf &&\n                child.lines + last.lines <= 32 /* Tree.Branch */) {\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk[currentChunk.length - 1] = new TextLeaf(last.text.concat(child.text), last.length + 1 + child.length);\n            }\n            else {\n                if (currentLines + child.lines > chunk)\n                    flush();\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk.push(child);\n            }\n        }\n        function flush() {\n            if (currentLines == 0)\n                return;\n            chunked.push(currentChunk.length == 1 ? currentChunk[0] : TextNode.from(currentChunk, currentLen));\n            currentLen = -1;\n            currentLines = currentChunk.length = 0;\n        }\n        for (let child of children)\n            add(child);\n        flush();\n        return chunked.length == 1 ? chunked[0] : new TextNode(chunked, length);\n    }\n}\nText.empty = /*@__PURE__*/new TextLeaf([\"\"], 0);\nfunction textLength(text) {\n    let length = -1;\n    for (let line of text)\n        length += line.length + 1;\n    return length;\n}\nfunction appendText(text, target, from = 0, to = 1e9) {\n    for (let pos = 0, i = 0, first = true; i < text.length && pos <= to; i++) {\n        let line = text[i], end = pos + line.length;\n        if (end >= from) {\n            if (end > to)\n                line = line.slice(0, to - pos);\n            if (pos < from)\n                line = line.slice(from - pos);\n            if (first) {\n                target[target.length - 1] += line;\n                first = false;\n            }\n            else\n                target.push(line);\n        }\n        pos = end + 1;\n    }\n    return target;\n}\nfunction sliceText(text, from, to) {\n    return appendText(text, [\"\"], from, to);\n}\nclass RawTextCursor {\n    constructor(text, dir = 1) {\n        this.dir = dir;\n        this.done = false;\n        this.lineBreak = false;\n        this.value = \"\";\n        this.nodes = [text];\n        this.offsets = [dir > 0 ? 1 : (text instanceof TextLeaf ? text.text.length : text.children.length) << 1];\n    }\n    nextInner(skip, dir) {\n        this.done = this.lineBreak = false;\n        for (;;) {\n            let last = this.nodes.length - 1;\n            let top = this.nodes[last], offsetValue = this.offsets[last], offset = offsetValue >> 1;\n            let size = top instanceof TextLeaf ? top.text.length : top.children.length;\n            if (offset == (dir > 0 ? size : 0)) {\n                if (last == 0) {\n                    this.done = true;\n                    this.value = \"\";\n                    return this;\n                }\n                if (dir > 0)\n                    this.offsets[last - 1]++;\n                this.nodes.pop();\n                this.offsets.pop();\n            }\n            else if ((offsetValue & 1) == (dir > 0 ? 0 : 1)) {\n                this.offsets[last] += dir;\n                if (skip == 0) {\n                    this.lineBreak = true;\n                    this.value = \"\\n\";\n                    return this;\n                }\n                skip--;\n            }\n            else if (top instanceof TextLeaf) {\n                // Move to the next string\n                let next = top.text[offset + (dir < 0 ? -1 : 0)];\n                this.offsets[last] += dir;\n                if (next.length > Math.max(0, skip)) {\n                    this.value = skip == 0 ? next : dir > 0 ? next.slice(skip) : next.slice(0, next.length - skip);\n                    return this;\n                }\n                skip -= next.length;\n            }\n            else {\n                let next = top.children[offset + (dir < 0 ? -1 : 0)];\n                if (skip > next.length) {\n                    skip -= next.length;\n                    this.offsets[last] += dir;\n                }\n                else {\n                    if (dir < 0)\n                        this.offsets[last]--;\n                    this.nodes.push(next);\n                    this.offsets.push(dir > 0 ? 1 : (next instanceof TextLeaf ? next.text.length : next.children.length) << 1);\n                }\n            }\n        }\n    }\n    next(skip = 0) {\n        if (skip < 0) {\n            this.nextInner(-skip, (-this.dir));\n            skip = this.value.length;\n        }\n        return this.nextInner(skip, this.dir);\n    }\n}\nclass PartialTextCursor {\n    constructor(text, start, end) {\n        this.value = \"\";\n        this.done = false;\n        this.cursor = new RawTextCursor(text, start > end ? -1 : 1);\n        this.pos = start > end ? text.length : 0;\n        this.from = Math.min(start, end);\n        this.to = Math.max(start, end);\n    }\n    nextInner(skip, dir) {\n        if (dir < 0 ? this.pos <= this.from : this.pos >= this.to) {\n            this.value = \"\";\n            this.done = true;\n            return this;\n        }\n        skip += Math.max(0, dir < 0 ? this.pos - this.to : this.from - this.pos);\n        let limit = dir < 0 ? this.pos - this.from : this.to - this.pos;\n        if (skip > limit)\n            skip = limit;\n        limit -= skip;\n        let { value } = this.cursor.next(skip);\n        this.pos += (value.length + skip) * dir;\n        this.value = value.length <= limit ? value : dir < 0 ? value.slice(value.length - limit) : value.slice(0, limit);\n        this.done = !this.value;\n        return this;\n    }\n    next(skip = 0) {\n        if (skip < 0)\n            skip = Math.max(skip, this.from - this.pos);\n        else if (skip > 0)\n            skip = Math.min(skip, this.to - this.pos);\n        return this.nextInner(skip, this.cursor.dir);\n    }\n    get lineBreak() { return this.cursor.lineBreak && this.value != \"\"; }\n}\nclass LineCursor {\n    constructor(inner) {\n        this.inner = inner;\n        this.afterBreak = true;\n        this.value = \"\";\n        this.done = false;\n    }\n    next(skip = 0) {\n        let { done, lineBreak, value } = this.inner.next(skip);\n        if (done && this.afterBreak) {\n            this.value = \"\";\n            this.afterBreak = false;\n        }\n        else if (done) {\n            this.done = true;\n            this.value = \"\";\n        }\n        else if (lineBreak) {\n            if (this.afterBreak) {\n                this.value = \"\";\n            }\n            else {\n                this.afterBreak = true;\n                this.next();\n            }\n        }\n        else {\n            this.value = value;\n            this.afterBreak = false;\n        }\n        return this;\n    }\n    get lineBreak() { return false; }\n}\nif (typeof Symbol != \"undefined\") {\n    Text.prototype[Symbol.iterator] = function () { return this.iter(); };\n    RawTextCursor.prototype[Symbol.iterator] = PartialTextCursor.prototype[Symbol.iterator] =\n        LineCursor.prototype[Symbol.iterator] = function () { return this; };\n}\n/**\nThis type describes a line in the document. It is created\non-demand when lines are [queried](https://codemirror.net/6/docs/ref/#state.Text.lineAt).\n*/\nclass Line {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position of the start of the line.\n    */\n    from, \n    /**\n    The position at the end of the line (_before_ the line break,\n    or at the end of document for the last line).\n    */\n    to, \n    /**\n    This line's line number (1-based).\n    */\n    number, \n    /**\n    The line's content.\n    */\n    text) {\n        this.from = from;\n        this.to = to;\n        this.number = number;\n        this.text = text;\n    }\n    /**\n    The length of the line (not including any line break after it).\n    */\n    get length() { return this.to - this.from; }\n}\nfunction clip(text, from, to) {\n    from = Math.max(0, Math.min(text.length, from));\n    return [from, Math.max(from, Math.min(text.length, to))];\n}\n\n/**\nReturns a next grapheme cluster break _after_ (not equal to)\n`pos`, if `forward` is true, or before otherwise. Returns `pos`\nitself if no further cluster break is available in the string.\nMoves across surrogate pairs, extending characters (when\n`includeExtending` is true), characters joined with zero-width\njoiners, and flag emoji.\n*/\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n    return findClusterBreak$1(str, pos, forward, includeExtending);\n}\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000; }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00; }\n/**\nFind the code point at the given position in a string (like the\n[`codePointAt`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/codePointAt)\nstring method).\n*/\nfunction codePointAt(str, pos) {\n    let code0 = str.charCodeAt(pos);\n    if (!surrogateHigh(code0) || pos + 1 == str.length)\n        return code0;\n    let code1 = str.charCodeAt(pos + 1);\n    if (!surrogateLow(code1))\n        return code0;\n    return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000;\n}\n/**\nGiven a Unicode codepoint, return the JavaScript string that\nrespresents it (like\n[`String.fromCodePoint`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/fromCodePoint)).\n*/\nfunction fromCodePoint(code) {\n    if (code <= 0xffff)\n        return String.fromCharCode(code);\n    code -= 0x10000;\n    return String.fromCharCode((code >> 10) + 0xd800, (code & 1023) + 0xdc00);\n}\n/**\nThe amount of positions a character takes up in a JavaScript string.\n*/\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2; }\n\nconst DefaultSplit = /\\r\\n?|\\n/;\n/**\nDistinguishes different ways in which positions can be mapped.\n*/\nvar MapMode = /*@__PURE__*/(function (MapMode) {\n    /**\n    Map a position to a valid new position, even when its context\n    was deleted.\n    */\n    MapMode[MapMode[\"Simple\"] = 0] = \"Simple\";\n    /**\n    Return null if deletion happens across the position.\n    */\n    MapMode[MapMode[\"TrackDel\"] = 1] = \"TrackDel\";\n    /**\n    Return null if the character _before_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackBefore\"] = 2] = \"TrackBefore\";\n    /**\n    Return null if the character _after_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackAfter\"] = 3] = \"TrackAfter\";\nreturn MapMode})(MapMode || (MapMode = {}));\n/**\nA change description is a variant of [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet)\nthat doesn't store the inserted text. As such, it can't be\napplied, but is cheaper to store and manipulate.\n*/\nclass ChangeDesc {\n    // Sections are encoded as pairs of integers. The first is the\n    // length in the current document, and the second is -1 for\n    // unaffected sections, and the length of the replacement content\n    // otherwise. So an insertion would be (0, n>0), a deletion (n>0,\n    // 0), and a replacement two positive numbers.\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    sections) {\n        this.sections = sections;\n    }\n    /**\n    The length of the document before the change.\n    */\n    get length() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2)\n            result += this.sections[i];\n        return result;\n    }\n    /**\n    The length of the document after the change.\n    */\n    get newLength() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let ins = this.sections[i + 1];\n            result += ins < 0 ? this.sections[i] : ins;\n        }\n        return result;\n    }\n    /**\n    False when there are actual changes in this set.\n    */\n    get empty() { return this.sections.length == 0 || this.sections.length == 2 && this.sections[1] < 0; }\n    /**\n    Iterate over the unchanged parts left by these changes. `posA`\n    provides the position of the range in the old document, `posB`\n    the new position in the changed document.\n    */\n    iterGaps(f) {\n        for (let i = 0, posA = 0, posB = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0) {\n                f(posA, posB, len);\n                posB += len;\n            }\n            else {\n                posB += ins;\n            }\n            posA += len;\n        }\n    }\n    /**\n    Iterate over the ranges changed by these changes. (See\n    [`ChangeSet.iterChanges`](https://codemirror.net/6/docs/ref/#state.ChangeSet.iterChanges) for a\n    variant that also provides you with the inserted text.)\n    `fromA`/`toA` provides the extent of the change in the starting\n    document, `fromB`/`toB` the extent of the replacement in the\n    changed document.\n    \n    When `individual` is true, adjacent changes (which are kept\n    separate for [position mapping](https://codemirror.net/6/docs/ref/#state.ChangeDesc.mapPos)) are\n    reported separately.\n    */\n    iterChangedRanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a description of the inverted form of these changes.\n    */\n    get invertedDesc() {\n        let sections = [];\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0)\n                sections.push(len, ins);\n            else\n                sections.push(ins, len);\n        }\n        return new ChangeDesc(sections);\n    }\n    /**\n    Compute the combined effect of applying another set of changes\n    after this one. The length of the document after this set should\n    match the length before `other`.\n    */\n    composeDesc(other) { return this.empty ? other : other.empty ? this : composeSets(this, other); }\n    /**\n    Map this description, which should start with the same document\n    as `other`, over another set of changes, so that it can be\n    applied after it. When `before` is true, map as if the changes\n    in `this` happened before the ones in `other`.\n    */\n    mapDesc(other, before = false) { return other.empty ? this : mapSet(this, other, before); }\n    mapPos(pos, assoc = -1, mode = MapMode.Simple) {\n        let posA = 0, posB = 0;\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++], endA = posA + len;\n            if (ins < 0) {\n                if (endA > pos)\n                    return posB + (pos - posA);\n                posB += len;\n            }\n            else {\n                if (mode != MapMode.Simple && endA >= pos &&\n                    (mode == MapMode.TrackDel && posA < pos && endA > pos ||\n                        mode == MapMode.TrackBefore && posA < pos ||\n                        mode == MapMode.TrackAfter && endA > pos))\n                    return null;\n                if (endA > pos || endA == pos && assoc < 0 && !len)\n                    return pos == posA || assoc < 0 ? posB : posB + ins;\n                posB += ins;\n            }\n            posA = endA;\n        }\n        if (pos > posA)\n            throw new RangeError(`Position ${pos} is out of range for changeset of length ${posA}`);\n        return posB;\n    }\n    /**\n    Check whether these changes touch a given range. When one of the\n    changes entirely covers the range, the string `\"cover\"` is\n    returned.\n    */\n    touchesRange(from, to = from) {\n        for (let i = 0, pos = 0; i < this.sections.length && pos <= to;) {\n            let len = this.sections[i++], ins = this.sections[i++], end = pos + len;\n            if (ins >= 0 && pos <= to && end >= from)\n                return pos < from && end > to ? \"cover\" : true;\n            pos = end;\n        }\n        return false;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let result = \"\";\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            result += (result ? \" \" : \"\") + len + (ins >= 0 ? \":\" + ins : \"\");\n        }\n        return result;\n    }\n    /**\n    Serialize this change desc to a JSON-representable value.\n    */\n    toJSON() { return this.sections; }\n    /**\n    Create a change desc from its JSON representation (as produced\n    by [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeDesc.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json) || json.length % 2 || json.some(a => typeof a != \"number\"))\n            throw new RangeError(\"Invalid JSON representation of ChangeDesc\");\n        return new ChangeDesc(json);\n    }\n    /**\n    @internal\n    */\n    static create(sections) { return new ChangeDesc(sections); }\n}\n/**\nA change set represents a group of modifications to a document. It\nstores the document length, and can only be applied to documents\nwith exactly that length.\n*/\nclass ChangeSet extends ChangeDesc {\n    constructor(sections, \n    /**\n    @internal\n    */\n    inserted) {\n        super(sections);\n        this.inserted = inserted;\n    }\n    /**\n    Apply the changes to a document, returning the modified\n    document.\n    */\n    apply(doc) {\n        if (this.length != doc.length)\n            throw new RangeError(\"Applying change set to a document with the wrong length\");\n        iterChanges(this, (fromA, toA, fromB, _toB, text) => doc = doc.replace(fromB, fromB + (toA - fromA), text), false);\n        return doc;\n    }\n    mapDesc(other, before = false) { return mapSet(this, other, before, true); }\n    /**\n    Given the document as it existed _before_ the changes, return a\n    change set that represents the inverse of this set, which could\n    be used to go from the document created by the changes back to\n    the document as it existed before the changes.\n    */\n    invert(doc) {\n        let sections = this.sections.slice(), inserted = [];\n        for (let i = 0, pos = 0; i < sections.length; i += 2) {\n            let len = sections[i], ins = sections[i + 1];\n            if (ins >= 0) {\n                sections[i] = ins;\n                sections[i + 1] = len;\n                let index = i >> 1;\n                while (inserted.length < index)\n                    inserted.push(Text.empty);\n                inserted.push(len ? doc.slice(pos, pos + len) : Text.empty);\n            }\n            pos += len;\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    Combine two subsequent change sets into a single set. `other`\n    must start in the document produced by `this`. If `this` goes\n    `docA` → `docB` and `other` represents `docB` → `docC`, the\n    returned value will represent the change `docA` → `docC`.\n    */\n    compose(other) { return this.empty ? other : other.empty ? this : composeSets(this, other, true); }\n    /**\n    Given another change set starting in the same document, maps this\n    change set over the other, producing a new change set that can be\n    applied to the document produced by applying `other`. When\n    `before` is `true`, order changes as if `this` comes before\n    `other`, otherwise (the default) treat `other` as coming first.\n    \n    Given two changes `A` and `B`, `A.compose(B.map(A))` and\n    `B.compose(A.map(B, true))` will produce the same document. This\n    provides a basic form of [operational\n    transformation](https://en.wikipedia.org/wiki/Operational_transformation),\n    and can be used for collaborative editing.\n    */\n    map(other, before = false) { return other.empty ? this : mapSet(this, other, before, true); }\n    /**\n    Iterate over the changed ranges in the document, calling `f` for\n    each, with the range in the original document (`fromA`-`toA`)\n    and the range that replaces it in the new document\n    (`fromB`-`toB`).\n    \n    When `individual` is true, adjacent changes are reported\n    separately.\n    */\n    iterChanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a [change description](https://codemirror.net/6/docs/ref/#state.ChangeDesc) for this change\n    set.\n    */\n    get desc() { return ChangeDesc.create(this.sections); }\n    /**\n    @internal\n    */\n    filter(ranges) {\n        let resultSections = [], resultInserted = [], filteredSections = [];\n        let iter = new SectionIter(this);\n        done: for (let i = 0, pos = 0;;) {\n            let next = i == ranges.length ? 1e9 : ranges[i++];\n            while (pos < next || pos == next && iter.len == 0) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, next - pos);\n                addSection(filteredSections, len, -1);\n                let ins = iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0;\n                addSection(resultSections, len, ins);\n                if (ins > 0)\n                    addInsert(resultInserted, resultSections, iter.text);\n                iter.forward(len);\n                pos += len;\n            }\n            let end = ranges[i++];\n            while (pos < end) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, end - pos);\n                addSection(resultSections, len, -1);\n                addSection(filteredSections, len, iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0);\n                iter.forward(len);\n                pos += len;\n            }\n        }\n        return { changes: new ChangeSet(resultSections, resultInserted),\n            filtered: ChangeDesc.create(filteredSections) };\n    }\n    /**\n    Serialize this change set to a JSON-representable value.\n    */\n    toJSON() {\n        let parts = [];\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let len = this.sections[i], ins = this.sections[i + 1];\n            if (ins < 0)\n                parts.push(len);\n            else if (ins == 0)\n                parts.push([len]);\n            else\n                parts.push([len].concat(this.inserted[i >> 1].toJSON()));\n        }\n        return parts;\n    }\n    /**\n    Create a change set for the given changes, for a document of the\n    given length, using `lineSep` as line separator.\n    */\n    static of(changes, length, lineSep) {\n        let sections = [], inserted = [], pos = 0;\n        let total = null;\n        function flush(force = false) {\n            if (!force && !sections.length)\n                return;\n            if (pos < length)\n                addSection(sections, length - pos, -1);\n            let set = new ChangeSet(sections, inserted);\n            total = total ? total.compose(set.map(total)) : set;\n            sections = [];\n            inserted = [];\n            pos = 0;\n        }\n        function process(spec) {\n            if (Array.isArray(spec)) {\n                for (let sub of spec)\n                    process(sub);\n            }\n            else if (spec instanceof ChangeSet) {\n                if (spec.length != length)\n                    throw new RangeError(`Mismatched change set length (got ${spec.length}, expected ${length})`);\n                flush();\n                total = total ? total.compose(spec.map(total)) : spec;\n            }\n            else {\n                let { from, to = from, insert } = spec;\n                if (from > to || from < 0 || to > length)\n                    throw new RangeError(`Invalid change range ${from} to ${to} (in doc of length ${length})`);\n                let insText = !insert ? Text.empty : typeof insert == \"string\" ? Text.of(insert.split(lineSep || DefaultSplit)) : insert;\n                let insLen = insText.length;\n                if (from == to && insLen == 0)\n                    return;\n                if (from < pos)\n                    flush();\n                if (from > pos)\n                    addSection(sections, from - pos, -1);\n                addSection(sections, to - from, insLen);\n                addInsert(inserted, sections, insText);\n                pos = to;\n            }\n        }\n        process(changes);\n        flush(!total);\n        return total;\n    }\n    /**\n    Create an empty changeset of the given length.\n    */\n    static empty(length) {\n        return new ChangeSet(length ? [length, -1] : [], []);\n    }\n    /**\n    Create a changeset from its JSON representation (as produced by\n    [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeSet.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json))\n            throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n        let sections = [], inserted = [];\n        for (let i = 0; i < json.length; i++) {\n            let part = json[i];\n            if (typeof part == \"number\") {\n                sections.push(part, -1);\n            }\n            else if (!Array.isArray(part) || typeof part[0] != \"number\" || part.some((e, i) => i && typeof e != \"string\")) {\n                throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n            }\n            else if (part.length == 1) {\n                sections.push(part[0], 0);\n            }\n            else {\n                while (inserted.length < i)\n                    inserted.push(Text.empty);\n                inserted[i] = Text.of(part.slice(1));\n                sections.push(part[0], inserted[i].length);\n            }\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    @internal\n    */\n    static createSet(sections, inserted) {\n        return new ChangeSet(sections, inserted);\n    }\n}\nfunction addSection(sections, len, ins, forceJoin = false) {\n    if (len == 0 && ins <= 0)\n        return;\n    let last = sections.length - 2;\n    if (last >= 0 && ins <= 0 && ins == sections[last + 1])\n        sections[last] += len;\n    else if (last >= 0 && len == 0 && sections[last] == 0)\n        sections[last + 1] += ins;\n    else if (forceJoin) {\n        sections[last] += len;\n        sections[last + 1] += ins;\n    }\n    else\n        sections.push(len, ins);\n}\nfunction addInsert(values, sections, value) {\n    if (value.length == 0)\n        return;\n    let index = (sections.length - 2) >> 1;\n    if (index < values.length) {\n        values[values.length - 1] = values[values.length - 1].append(value);\n    }\n    else {\n        while (values.length < index)\n            values.push(Text.empty);\n        values.push(value);\n    }\n}\nfunction iterChanges(desc, f, individual) {\n    let inserted = desc.inserted;\n    for (let posA = 0, posB = 0, i = 0; i < desc.sections.length;) {\n        let len = desc.sections[i++], ins = desc.sections[i++];\n        if (ins < 0) {\n            posA += len;\n            posB += len;\n        }\n        else {\n            let endA = posA, endB = posB, text = Text.empty;\n            for (;;) {\n                endA += len;\n                endB += ins;\n                if (ins && inserted)\n                    text = text.append(inserted[(i - 2) >> 1]);\n                if (individual || i == desc.sections.length || desc.sections[i + 1] < 0)\n                    break;\n                len = desc.sections[i++];\n                ins = desc.sections[i++];\n            }\n            f(posA, endA, posB, endB, text);\n            posA = endA;\n            posB = endB;\n        }\n    }\n}\nfunction mapSet(setA, setB, before, mkSet = false) {\n    // Produce a copy of setA that applies to the document after setB\n    // has been applied (assuming both start at the same document).\n    let sections = [], insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    // Iterate over both sets in parallel. inserted tracks, for changes\n    // in A that have to be processed piece-by-piece, whether their\n    // content has been inserted already, and refers to the section\n    // index.\n    for (let inserted = -1;;) {\n        if (a.done && b.len || b.done && a.len) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else if (a.ins == -1 && b.ins == -1) {\n            // Move across ranges skipped by both sets.\n            let len = Math.min(a.len, b.len);\n            addSection(sections, len, -1);\n            a.forward(len);\n            b.forward(len);\n        }\n        else if (b.ins >= 0 && (a.ins < 0 || inserted == a.i || a.off == 0 && (b.len < a.len || b.len == a.len && !before))) {\n            // If there's a change in B that comes before the next change in\n            // A (ordered by start pos, then len, then before flag), skip\n            // that (and process any changes in A it covers).\n            let len = b.len;\n            addSection(sections, b.ins, -1);\n            while (len) {\n                let piece = Math.min(a.len, len);\n                if (a.ins >= 0 && inserted < a.i && a.len <= piece) {\n                    addSection(sections, 0, a.ins);\n                    if (insert)\n                        addInsert(insert, sections, a.text);\n                    inserted = a.i;\n                }\n                a.forward(piece);\n                len -= piece;\n            }\n            b.next();\n        }\n        else if (a.ins >= 0) {\n            // Process the part of a change in A up to the start of the next\n            // non-deletion change in B (if overlapping).\n            let len = 0, left = a.len;\n            while (left) {\n                if (b.ins == -1) {\n                    let piece = Math.min(left, b.len);\n                    len += piece;\n                    left -= piece;\n                    b.forward(piece);\n                }\n                else if (b.ins == 0 && b.len < left) {\n                    left -= b.len;\n                    b.next();\n                }\n                else {\n                    break;\n                }\n            }\n            addSection(sections, len, inserted < a.i ? a.ins : 0);\n            if (insert && inserted < a.i)\n                addInsert(insert, sections, a.text);\n            inserted = a.i;\n            a.forward(a.len - left);\n        }\n        else if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n    }\n}\nfunction composeSets(setA, setB, mkSet = false) {\n    let sections = [];\n    let insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    for (let open = false;;) {\n        if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else if (a.ins == 0) { // Deletion in A\n            addSection(sections, a.len, 0, open);\n            a.next();\n        }\n        else if (b.len == 0 && !b.done) { // Insertion in B\n            addSection(sections, 0, b.ins, open);\n            if (insert)\n                addInsert(insert, sections, b.text);\n            b.next();\n        }\n        else if (a.done || b.done) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else {\n            let len = Math.min(a.len2, b.len), sectionLen = sections.length;\n            if (a.ins == -1) {\n                let insB = b.ins == -1 ? -1 : b.off ? 0 : b.ins;\n                addSection(sections, len, insB, open);\n                if (insert && insB)\n                    addInsert(insert, sections, b.text);\n            }\n            else if (b.ins == -1) {\n                addSection(sections, a.off ? 0 : a.len, len, open);\n                if (insert)\n                    addInsert(insert, sections, a.textBit(len));\n            }\n            else {\n                addSection(sections, a.off ? 0 : a.len, b.off ? 0 : b.ins, open);\n                if (insert && !b.off)\n                    addInsert(insert, sections, b.text);\n            }\n            open = (a.ins > len || b.ins >= 0 && b.len > len) && (open || sections.length > sectionLen);\n            a.forward2(len);\n            b.forward(len);\n        }\n    }\n}\nclass SectionIter {\n    constructor(set) {\n        this.set = set;\n        this.i = 0;\n        this.next();\n    }\n    next() {\n        let { sections } = this.set;\n        if (this.i < sections.length) {\n            this.len = sections[this.i++];\n            this.ins = sections[this.i++];\n        }\n        else {\n            this.len = 0;\n            this.ins = -2;\n        }\n        this.off = 0;\n    }\n    get done() { return this.ins == -2; }\n    get len2() { return this.ins < 0 ? this.len : this.ins; }\n    get text() {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length ? Text.empty : inserted[index];\n    }\n    textBit(len) {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length && !len ? Text.empty\n            : inserted[index].slice(this.off, len == null ? undefined : this.off + len);\n    }\n    forward(len) {\n        if (len == this.len)\n            this.next();\n        else {\n            this.len -= len;\n            this.off += len;\n        }\n    }\n    forward2(len) {\n        if (this.ins == -1)\n            this.forward(len);\n        else if (len == this.ins)\n            this.next();\n        else {\n            this.ins -= len;\n            this.off += len;\n        }\n    }\n}\n\n/**\nA single selection range. When\n[`allowMultipleSelections`](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\nis enabled, a [selection](https://codemirror.net/6/docs/ref/#state.EditorSelection) may hold\nmultiple ranges. By default, selections hold exactly one range.\n*/\nclass SelectionRange {\n    constructor(\n    /**\n    The lower boundary of the range.\n    */\n    from, \n    /**\n    The upper boundary of the range.\n    */\n    to, flags) {\n        this.from = from;\n        this.to = to;\n        this.flags = flags;\n    }\n    /**\n    The anchor of the range—the side that doesn't move when you\n    extend it.\n    */\n    get anchor() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.to : this.from; }\n    /**\n    The head of the range, which is moved when the range is\n    [extended](https://codemirror.net/6/docs/ref/#state.SelectionRange.extend).\n    */\n    get head() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.from : this.to; }\n    /**\n    True when `anchor` and `head` are at the same position.\n    */\n    get empty() { return this.from == this.to; }\n    /**\n    If this is a cursor that is explicitly associated with the\n    character on one of its sides, this returns the side. -1 means\n    the character before its position, 1 the character after, and 0\n    means no association.\n    */\n    get assoc() { return this.flags & 8 /* RangeFlag.AssocBefore */ ? -1 : this.flags & 16 /* RangeFlag.AssocAfter */ ? 1 : 0; }\n    /**\n    The bidirectional text level associated with this cursor, if\n    any.\n    */\n    get bidiLevel() {\n        let level = this.flags & 7 /* RangeFlag.BidiLevelMask */;\n        return level == 7 ? null : level;\n    }\n    /**\n    The goal column (stored vertical offset) associated with a\n    cursor. This is used to preserve the vertical position when\n    [moving](https://codemirror.net/6/docs/ref/#view.EditorView.moveVertically) across\n    lines of different length.\n    */\n    get goalColumn() {\n        let value = this.flags >> 6 /* RangeFlag.GoalColumnOffset */;\n        return value == 16777215 /* RangeFlag.NoGoalColumn */ ? undefined : value;\n    }\n    /**\n    Map this range through a change, producing a valid range in the\n    updated document.\n    */\n    map(change, assoc = -1) {\n        let from, to;\n        if (this.empty) {\n            from = to = change.mapPos(this.from, assoc);\n        }\n        else {\n            from = change.mapPos(this.from, 1);\n            to = change.mapPos(this.to, -1);\n        }\n        return from == this.from && to == this.to ? this : new SelectionRange(from, to, this.flags);\n    }\n    /**\n    Extend this range to cover at least `from` to `to`.\n    */\n    extend(from, to = from) {\n        if (from <= this.anchor && to >= this.anchor)\n            return EditorSelection.range(from, to);\n        let head = Math.abs(from - this.anchor) > Math.abs(to - this.anchor) ? from : to;\n        return EditorSelection.range(this.anchor, head);\n    }\n    /**\n    Compare this range to another range.\n    */\n    eq(other, includeAssoc = false) {\n        return this.anchor == other.anchor && this.head == other.head &&\n            (!includeAssoc || !this.empty || this.assoc == other.assoc);\n    }\n    /**\n    Return a JSON-serializable object representing the range.\n    */\n    toJSON() { return { anchor: this.anchor, head: this.head }; }\n    /**\n    Convert a JSON representation of a range to a `SelectionRange`\n    instance.\n    */\n    static fromJSON(json) {\n        if (!json || typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid JSON representation for SelectionRange\");\n        return EditorSelection.range(json.anchor, json.head);\n    }\n    /**\n    @internal\n    */\n    static create(from, to, flags) {\n        return new SelectionRange(from, to, flags);\n    }\n}\n/**\nAn editor selection holds one or more selection ranges.\n*/\nclass EditorSelection {\n    constructor(\n    /**\n    The ranges in the selection, sorted by position. Ranges cannot\n    overlap (but they may touch, if they aren't empty).\n    */\n    ranges, \n    /**\n    The index of the _main_ range in the selection (which is\n    usually the range that was added last).\n    */\n    mainIndex) {\n        this.ranges = ranges;\n        this.mainIndex = mainIndex;\n    }\n    /**\n    Map a selection through a change. Used to adjust the selection\n    position for changes.\n    */\n    map(change, assoc = -1) {\n        if (change.empty)\n            return this;\n        return EditorSelection.create(this.ranges.map(r => r.map(change, assoc)), this.mainIndex);\n    }\n    /**\n    Compare this selection to another selection. By default, ranges\n    are compared only by position. When `includeAssoc` is true,\n    cursor ranges must also have the same\n    [`assoc`](https://codemirror.net/6/docs/ref/#state.SelectionRange.assoc) value.\n    */\n    eq(other, includeAssoc = false) {\n        if (this.ranges.length != other.ranges.length ||\n            this.mainIndex != other.mainIndex)\n            return false;\n        for (let i = 0; i < this.ranges.length; i++)\n            if (!this.ranges[i].eq(other.ranges[i], includeAssoc))\n                return false;\n        return true;\n    }\n    /**\n    Get the primary selection range. Usually, you should make sure\n    your code applies to _all_ ranges, by using methods like\n    [`changeByRange`](https://codemirror.net/6/docs/ref/#state.EditorState.changeByRange).\n    */\n    get main() { return this.ranges[this.mainIndex]; }\n    /**\n    Make sure the selection only has one range. Returns a selection\n    holding only the main range from this selection.\n    */\n    asSingle() {\n        return this.ranges.length == 1 ? this : new EditorSelection([this.main], 0);\n    }\n    /**\n    Extend this selection with an extra range.\n    */\n    addRange(range, main = true) {\n        return EditorSelection.create([range].concat(this.ranges), main ? 0 : this.mainIndex + 1);\n    }\n    /**\n    Replace a given range with another range, and then normalize the\n    selection to merge and sort ranges if necessary.\n    */\n    replaceRange(range, which = this.mainIndex) {\n        let ranges = this.ranges.slice();\n        ranges[which] = range;\n        return EditorSelection.create(ranges, this.mainIndex);\n    }\n    /**\n    Convert this selection to an object that can be serialized to\n    JSON.\n    */\n    toJSON() {\n        return { ranges: this.ranges.map(r => r.toJSON()), main: this.mainIndex };\n    }\n    /**\n    Create a selection from a JSON representation.\n    */\n    static fromJSON(json) {\n        if (!json || !Array.isArray(json.ranges) || typeof json.main != \"number\" || json.main >= json.ranges.length)\n            throw new RangeError(\"Invalid JSON representation for EditorSelection\");\n        return new EditorSelection(json.ranges.map((r) => SelectionRange.fromJSON(r)), json.main);\n    }\n    /**\n    Create a selection holding a single range.\n    */\n    static single(anchor, head = anchor) {\n        return new EditorSelection([EditorSelection.range(anchor, head)], 0);\n    }\n    /**\n    Sort and merge the given set of ranges, creating a valid\n    selection.\n    */\n    static create(ranges, mainIndex = 0) {\n        if (ranges.length == 0)\n            throw new RangeError(\"A selection needs at least one range\");\n        for (let pos = 0, i = 0; i < ranges.length; i++) {\n            let range = ranges[i];\n            if (range.empty ? range.from <= pos : range.from < pos)\n                return EditorSelection.normalized(ranges.slice(), mainIndex);\n            pos = range.to;\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n    /**\n    Create a cursor selection range at the given position. You can\n    safely ignore the optional arguments in most situations.\n    */\n    static cursor(pos, assoc = 0, bidiLevel, goalColumn) {\n        return SelectionRange.create(pos, pos, (assoc == 0 ? 0 : assoc < 0 ? 8 /* RangeFlag.AssocBefore */ : 16 /* RangeFlag.AssocAfter */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel)) |\n            ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */));\n    }\n    /**\n    Create a selection range.\n    */\n    static range(anchor, head, goalColumn, bidiLevel) {\n        let flags = ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel));\n        return head < anchor ? SelectionRange.create(head, anchor, 32 /* RangeFlag.Inverted */ | 16 /* RangeFlag.AssocAfter */ | flags)\n            : SelectionRange.create(anchor, head, (head > anchor ? 8 /* RangeFlag.AssocBefore */ : 0) | flags);\n    }\n    /**\n    @internal\n    */\n    static normalized(ranges, mainIndex = 0) {\n        let main = ranges[mainIndex];\n        ranges.sort((a, b) => a.from - b.from);\n        mainIndex = ranges.indexOf(main);\n        for (let i = 1; i < ranges.length; i++) {\n            let range = ranges[i], prev = ranges[i - 1];\n            if (range.empty ? range.from <= prev.to : range.from < prev.to) {\n                let from = prev.from, to = Math.max(range.to, prev.to);\n                if (i <= mainIndex)\n                    mainIndex--;\n                ranges.splice(--i, 2, range.anchor > range.head ? EditorSelection.range(to, from) : EditorSelection.range(from, to));\n            }\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n}\nfunction checkSelection(selection, docLength) {\n    for (let range of selection.ranges)\n        if (range.to > docLength)\n            throw new RangeError(\"Selection points outside of document\");\n}\n\nlet nextID = 0;\n/**\nA facet is a labeled value that is associated with an editor\nstate. It takes inputs from any number of extensions, and combines\nthose into a single output value.\n\nExamples of uses of facets are the [tab\nsize](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize), [editor\nattributes](https://codemirror.net/6/docs/ref/#view.EditorView^editorAttributes), and [update\nlisteners](https://codemirror.net/6/docs/ref/#view.EditorView^updateListener).\n\nNote that `Facet` instances can be used anywhere where\n[`FacetReader`](https://codemirror.net/6/docs/ref/#state.FacetReader) is expected.\n*/\nclass Facet {\n    constructor(\n    /**\n    @internal\n    */\n    combine, \n    /**\n    @internal\n    */\n    compareInput, \n    /**\n    @internal\n    */\n    compare, isStatic, enables) {\n        this.combine = combine;\n        this.compareInput = compareInput;\n        this.compare = compare;\n        this.isStatic = isStatic;\n        /**\n        @internal\n        */\n        this.id = nextID++;\n        this.default = combine([]);\n        this.extensions = typeof enables == \"function\" ? enables(this) : enables;\n    }\n    /**\n    Returns a facet reader for this facet, which can be used to\n    [read](https://codemirror.net/6/docs/ref/#state.EditorState.facet) it but not to define values for it.\n    */\n    get reader() { return this; }\n    /**\n    Define a new facet.\n    */\n    static define(config = {}) {\n        return new Facet(config.combine || ((a) => a), config.compareInput || ((a, b) => a === b), config.compare || (!config.combine ? sameArray : (a, b) => a === b), !!config.static, config.enables);\n    }\n    /**\n    Returns an extension that adds the given value to this facet.\n    */\n    of(value) {\n        return new FacetProvider([], this, 0 /* Provider.Static */, value);\n    }\n    /**\n    Create an extension that computes a value for the facet from a\n    state. You must take care to declare the parts of the state that\n    this value depends on, since your function is only called again\n    for a new state when one of those parts changed.\n    \n    In cases where your value depends only on a single field, you'll\n    want to use the [`from`](https://codemirror.net/6/docs/ref/#state.Facet.from) method instead.\n    */\n    compute(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 1 /* Provider.Single */, get);\n    }\n    /**\n    Create an extension that computes zero or more values for this\n    facet from a state.\n    */\n    computeN(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 2 /* Provider.Multi */, get);\n    }\n    from(field, get) {\n        if (!get)\n            get = x => x;\n        return this.compute([field], state => get(state.field(field)));\n    }\n}\nfunction sameArray(a, b) {\n    return a == b || a.length == b.length && a.every((e, i) => e === b[i]);\n}\nclass FacetProvider {\n    constructor(dependencies, facet, type, value) {\n        this.dependencies = dependencies;\n        this.facet = facet;\n        this.type = type;\n        this.value = value;\n        this.id = nextID++;\n    }\n    dynamicSlot(addresses) {\n        var _a;\n        let getter = this.value;\n        let compare = this.facet.compareInput;\n        let id = this.id, idx = addresses[id] >> 1, multi = this.type == 2 /* Provider.Multi */;\n        let depDoc = false, depSel = false, depAddrs = [];\n        for (let dep of this.dependencies) {\n            if (dep == \"doc\")\n                depDoc = true;\n            else if (dep == \"selection\")\n                depSel = true;\n            else if ((((_a = addresses[dep.id]) !== null && _a !== void 0 ? _a : 1) & 1) == 0)\n                depAddrs.push(addresses[dep.id]);\n        }\n        return {\n            create(state) {\n                state.values[idx] = getter(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update(state, tr) {\n                if ((depDoc && tr.docChanged) || (depSel && (tr.docChanged || tr.selection)) || ensureAll(state, depAddrs)) {\n                    let newVal = getter(state);\n                    if (multi ? !compareArray(newVal, state.values[idx], compare) : !compare(newVal, state.values[idx])) {\n                        state.values[idx] = newVal;\n                        return 1 /* SlotStatus.Changed */;\n                    }\n                }\n                return 0;\n            },\n            reconfigure: (state, oldState) => {\n                let newVal, oldAddr = oldState.config.address[id];\n                if (oldAddr != null) {\n                    let oldVal = getAddr(oldState, oldAddr);\n                    if (this.dependencies.every(dep => {\n                        return dep instanceof Facet ? oldState.facet(dep) === state.facet(dep) :\n                            dep instanceof StateField ? oldState.field(dep, false) == state.field(dep, false) : true;\n                    }) || (multi ? compareArray(newVal = getter(state), oldVal, compare) : compare(newVal = getter(state), oldVal))) {\n                        state.values[idx] = oldVal;\n                        return 0;\n                    }\n                }\n                else {\n                    newVal = getter(state);\n                }\n                state.values[idx] = newVal;\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n}\nfunction compareArray(a, b, compare) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!compare(a[i], b[i]))\n            return false;\n    return true;\n}\nfunction ensureAll(state, addrs) {\n    let changed = false;\n    for (let addr of addrs)\n        if (ensureAddr(state, addr) & 1 /* SlotStatus.Changed */)\n            changed = true;\n    return changed;\n}\nfunction dynamicFacetSlot(addresses, facet, providers) {\n    let providerAddrs = providers.map(p => addresses[p.id]);\n    let providerTypes = providers.map(p => p.type);\n    let dynamic = providerAddrs.filter(p => !(p & 1));\n    let idx = addresses[facet.id] >> 1;\n    function get(state) {\n        let values = [];\n        for (let i = 0; i < providerAddrs.length; i++) {\n            let value = getAddr(state, providerAddrs[i]);\n            if (providerTypes[i] == 2 /* Provider.Multi */)\n                for (let val of value)\n                    values.push(val);\n            else\n                values.push(value);\n        }\n        return facet.combine(values);\n    }\n    return {\n        create(state) {\n            for (let addr of providerAddrs)\n                ensureAddr(state, addr);\n            state.values[idx] = get(state);\n            return 1 /* SlotStatus.Changed */;\n        },\n        update(state, tr) {\n            if (!ensureAll(state, dynamic))\n                return 0;\n            let value = get(state);\n            if (facet.compare(value, state.values[idx]))\n                return 0;\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        },\n        reconfigure(state, oldState) {\n            let depChanged = ensureAll(state, providerAddrs);\n            let oldProviders = oldState.config.facets[facet.id], oldValue = oldState.facet(facet);\n            if (oldProviders && !depChanged && sameArray(providers, oldProviders)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            let value = get(state);\n            if (facet.compare(value, oldValue)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        }\n    };\n}\nconst initField = /*@__PURE__*/Facet.define({ static: true });\n/**\nFields can store additional information in an editor state, and\nkeep it in sync with the rest of the state.\n*/\nclass StateField {\n    constructor(\n    /**\n    @internal\n    */\n    id, createF, updateF, compareF, \n    /**\n    @internal\n    */\n    spec) {\n        this.id = id;\n        this.createF = createF;\n        this.updateF = updateF;\n        this.compareF = compareF;\n        this.spec = spec;\n        /**\n        @internal\n        */\n        this.provides = undefined;\n    }\n    /**\n    Define a state field.\n    */\n    static define(config) {\n        let field = new StateField(nextID++, config.create, config.update, config.compare || ((a, b) => a === b), config);\n        if (config.provide)\n            field.provides = config.provide(field);\n        return field;\n    }\n    create(state) {\n        let init = state.facet(initField).find(i => i.field == this);\n        return ((init === null || init === void 0 ? void 0 : init.create) || this.createF)(state);\n    }\n    /**\n    @internal\n    */\n    slot(addresses) {\n        let idx = addresses[this.id] >> 1;\n        return {\n            create: (state) => {\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update: (state, tr) => {\n                let oldVal = state.values[idx];\n                let value = this.updateF(oldVal, tr);\n                if (this.compareF(oldVal, value))\n                    return 0;\n                state.values[idx] = value;\n                return 1 /* SlotStatus.Changed */;\n            },\n            reconfigure: (state, oldState) => {\n                let init = state.facet(initField), oldInit = oldState.facet(initField), reInit;\n                if ((reInit = init.find(i => i.field == this)) && reInit != oldInit.find(i => i.field == this)) {\n                    state.values[idx] = reInit.create(state);\n                    return 1 /* SlotStatus.Changed */;\n                }\n                if (oldState.config.address[this.id] != null) {\n                    state.values[idx] = oldState.field(this);\n                    return 0;\n                }\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n    /**\n    Returns an extension that enables this field and overrides the\n    way it is initialized. Can be useful when you need to provide a\n    non-default starting value for the field.\n    */\n    init(create) {\n        return [this, initField.of({ field: this, create })];\n    }\n    /**\n    State field instances can be used as\n    [`Extension`](https://codemirror.net/6/docs/ref/#state.Extension) values to enable the field in a\n    given state.\n    */\n    get extension() { return this; }\n}\nconst Prec_ = { lowest: 4, low: 3, default: 2, high: 1, highest: 0 };\nfunction prec(value) {\n    return (ext) => new PrecExtension(ext, value);\n}\n/**\nBy default extensions are registered in the order they are found\nin the flattened form of nested array that was provided.\nIndividual extension values can be assigned a precedence to\noverride this. Extensions that do not have a precedence set get\nthe precedence of the nearest parent with a precedence, or\n[`default`](https://codemirror.net/6/docs/ref/#state.Prec.default) if there is no such parent. The\nfinal ordering of extensions is determined by first sorting by\nprecedence and then by order within each precedence.\n*/\nconst Prec = {\n    /**\n    The highest precedence level, for extensions that should end up\n    near the start of the precedence ordering.\n    */\n    highest: /*@__PURE__*/prec(Prec_.highest),\n    /**\n    A higher-than-default precedence, for extensions that should\n    come before those with default precedence.\n    */\n    high: /*@__PURE__*/prec(Prec_.high),\n    /**\n    The default precedence, which is also used for extensions\n    without an explicit precedence.\n    */\n    default: /*@__PURE__*/prec(Prec_.default),\n    /**\n    A lower-than-default precedence.\n    */\n    low: /*@__PURE__*/prec(Prec_.low),\n    /**\n    The lowest precedence level. Meant for things that should end up\n    near the end of the extension order.\n    */\n    lowest: /*@__PURE__*/prec(Prec_.lowest)\n};\nclass PrecExtension {\n    constructor(inner, prec) {\n        this.inner = inner;\n        this.prec = prec;\n    }\n}\n/**\nExtension compartments can be used to make a configuration\ndynamic. By [wrapping](https://codemirror.net/6/docs/ref/#state.Compartment.of) part of your\nconfiguration in a compartment, you can later\n[replace](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure) that part through a\ntransaction.\n*/\nclass Compartment {\n    /**\n    Create an instance of this compartment to add to your [state\n    configuration](https://codemirror.net/6/docs/ref/#state.EditorStateConfig.extensions).\n    */\n    of(ext) { return new CompartmentInstance(this, ext); }\n    /**\n    Create an [effect](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) that\n    reconfigures this compartment.\n    */\n    reconfigure(content) {\n        return Compartment.reconfigure.of({ compartment: this, extension: content });\n    }\n    /**\n    Get the current content of the compartment in the state, or\n    `undefined` if it isn't present.\n    */\n    get(state) {\n        return state.config.compartments.get(this);\n    }\n}\nclass CompartmentInstance {\n    constructor(compartment, inner) {\n        this.compartment = compartment;\n        this.inner = inner;\n    }\n}\nclass Configuration {\n    constructor(base, compartments, dynamicSlots, address, staticValues, facets) {\n        this.base = base;\n        this.compartments = compartments;\n        this.dynamicSlots = dynamicSlots;\n        this.address = address;\n        this.staticValues = staticValues;\n        this.facets = facets;\n        this.statusTemplate = [];\n        while (this.statusTemplate.length < dynamicSlots.length)\n            this.statusTemplate.push(0 /* SlotStatus.Unresolved */);\n    }\n    staticFacet(facet) {\n        let addr = this.address[facet.id];\n        return addr == null ? facet.default : this.staticValues[addr >> 1];\n    }\n    static resolve(base, compartments, oldState) {\n        let fields = [];\n        let facets = Object.create(null);\n        let newCompartments = new Map();\n        for (let ext of flatten(base, compartments, newCompartments)) {\n            if (ext instanceof StateField)\n                fields.push(ext);\n            else\n                (facets[ext.facet.id] || (facets[ext.facet.id] = [])).push(ext);\n        }\n        let address = Object.create(null);\n        let staticValues = [];\n        let dynamicSlots = [];\n        for (let field of fields) {\n            address[field.id] = dynamicSlots.length << 1;\n            dynamicSlots.push(a => field.slot(a));\n        }\n        let oldFacets = oldState === null || oldState === void 0 ? void 0 : oldState.config.facets;\n        for (let id in facets) {\n            let providers = facets[id], facet = providers[0].facet;\n            let oldProviders = oldFacets && oldFacets[id] || [];\n            if (providers.every(p => p.type == 0 /* Provider.Static */)) {\n                address[facet.id] = (staticValues.length << 1) | 1;\n                if (sameArray(oldProviders, providers)) {\n                    staticValues.push(oldState.facet(facet));\n                }\n                else {\n                    let value = facet.combine(providers.map(p => p.value));\n                    staticValues.push(oldState && facet.compare(value, oldState.facet(facet)) ? oldState.facet(facet) : value);\n                }\n            }\n            else {\n                for (let p of providers) {\n                    if (p.type == 0 /* Provider.Static */) {\n                        address[p.id] = (staticValues.length << 1) | 1;\n                        staticValues.push(p.value);\n                    }\n                    else {\n                        address[p.id] = dynamicSlots.length << 1;\n                        dynamicSlots.push(a => p.dynamicSlot(a));\n                    }\n                }\n                address[facet.id] = dynamicSlots.length << 1;\n                dynamicSlots.push(a => dynamicFacetSlot(a, facet, providers));\n            }\n        }\n        let dynamic = dynamicSlots.map(f => f(address));\n        return new Configuration(base, newCompartments, dynamic, address, staticValues, facets);\n    }\n}\nfunction flatten(extension, compartments, newCompartments) {\n    let result = [[], [], [], [], []];\n    let seen = new Map();\n    function inner(ext, prec) {\n        let known = seen.get(ext);\n        if (known != null) {\n            if (known <= prec)\n                return;\n            let found = result[known].indexOf(ext);\n            if (found > -1)\n                result[known].splice(found, 1);\n            if (ext instanceof CompartmentInstance)\n                newCompartments.delete(ext.compartment);\n        }\n        seen.set(ext, prec);\n        if (Array.isArray(ext)) {\n            for (let e of ext)\n                inner(e, prec);\n        }\n        else if (ext instanceof CompartmentInstance) {\n            if (newCompartments.has(ext.compartment))\n                throw new RangeError(`Duplicate use of compartment in extensions`);\n            let content = compartments.get(ext.compartment) || ext.inner;\n            newCompartments.set(ext.compartment, content);\n            inner(content, prec);\n        }\n        else if (ext instanceof PrecExtension) {\n            inner(ext.inner, ext.prec);\n        }\n        else if (ext instanceof StateField) {\n            result[prec].push(ext);\n            if (ext.provides)\n                inner(ext.provides, prec);\n        }\n        else if (ext instanceof FacetProvider) {\n            result[prec].push(ext);\n            if (ext.facet.extensions)\n                inner(ext.facet.extensions, Prec_.default);\n        }\n        else {\n            let content = ext.extension;\n            if (!content)\n                throw new Error(`Unrecognized extension value in extension set (${ext}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);\n            inner(content, prec);\n        }\n    }\n    inner(extension, Prec_.default);\n    return result.reduce((a, b) => a.concat(b));\n}\nfunction ensureAddr(state, addr) {\n    if (addr & 1)\n        return 2 /* SlotStatus.Computed */;\n    let idx = addr >> 1;\n    let status = state.status[idx];\n    if (status == 4 /* SlotStatus.Computing */)\n        throw new Error(\"Cyclic dependency between fields and/or facets\");\n    if (status & 2 /* SlotStatus.Computed */)\n        return status;\n    state.status[idx] = 4 /* SlotStatus.Computing */;\n    let changed = state.computeSlot(state, state.config.dynamicSlots[idx]);\n    return state.status[idx] = 2 /* SlotStatus.Computed */ | changed;\n}\nfunction getAddr(state, addr) {\n    return addr & 1 ? state.config.staticValues[addr >> 1] : state.values[addr >> 1];\n}\n\nconst languageData = /*@__PURE__*/Facet.define();\nconst allowMultipleSelections = /*@__PURE__*/Facet.define({\n    combine: values => values.some(v => v),\n    static: true\n});\nconst lineSeparator = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : undefined,\n    static: true\n});\nconst changeFilter = /*@__PURE__*/Facet.define();\nconst transactionFilter = /*@__PURE__*/Facet.define();\nconst transactionExtender = /*@__PURE__*/Facet.define();\nconst readOnly = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : false\n});\n\n/**\nAnnotations are tagged values that are used to add metadata to\ntransactions in an extensible way. They should be used to model\nthings that effect the entire transaction (such as its [time\nstamp](https://codemirror.net/6/docs/ref/#state.Transaction^time) or information about its\n[origin](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent)). For effects that happen\n_alongside_ the other changes made by the transaction, [state\neffects](https://codemirror.net/6/docs/ref/#state.StateEffect) are more appropriate.\n*/\nclass Annotation {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The annotation type.\n    */\n    type, \n    /**\n    The value of this annotation.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Define a new type of annotation.\n    */\n    static define() { return new AnnotationType(); }\n}\n/**\nMarker that identifies a type of [annotation](https://codemirror.net/6/docs/ref/#state.Annotation).\n*/\nclass AnnotationType {\n    /**\n    Create an instance of this annotation.\n    */\n    of(value) { return new Annotation(this, value); }\n}\n/**\nRepresentation of a type of state effect. Defined with\n[`StateEffect.define`](https://codemirror.net/6/docs/ref/#state.StateEffect^define).\n*/\nclass StateEffectType {\n    /**\n    @internal\n    */\n    constructor(\n    // The `any` types in these function types are there to work\n    // around TypeScript issue #37631, where the type guard on\n    // `StateEffect.is` mysteriously stops working when these properly\n    // have type `Value`.\n    /**\n    @internal\n    */\n    map) {\n        this.map = map;\n    }\n    /**\n    Create a [state effect](https://codemirror.net/6/docs/ref/#state.StateEffect) instance of this\n    type.\n    */\n    of(value) { return new StateEffect(this, value); }\n}\n/**\nState effects can be used to represent additional effects\nassociated with a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction.effects). They\nare often useful to model changes to custom [state\nfields](https://codemirror.net/6/docs/ref/#state.StateField), when those changes aren't implicit in\ndocument or selection changes.\n*/\nclass StateEffect {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    type, \n    /**\n    The value of this effect.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Map this effect through a position mapping. Will return\n    `undefined` when that ends up deleting the effect.\n    */\n    map(mapping) {\n        let mapped = this.type.map(this.value, mapping);\n        return mapped === undefined ? undefined : mapped == this.value ? this : new StateEffect(this.type, mapped);\n    }\n    /**\n    Tells you whether this effect object is of a given\n    [type](https://codemirror.net/6/docs/ref/#state.StateEffectType).\n    */\n    is(type) { return this.type == type; }\n    /**\n    Define a new effect type. The type parameter indicates the type\n    of values that his effect holds. It should be a type that\n    doesn't include `undefined`, since that is used in\n    [mapping](https://codemirror.net/6/docs/ref/#state.StateEffect.map) to indicate that an effect is\n    removed.\n    */\n    static define(spec = {}) {\n        return new StateEffectType(spec.map || (v => v));\n    }\n    /**\n    Map an array of effects through a change set.\n    */\n    static mapEffects(effects, mapping) {\n        if (!effects.length)\n            return effects;\n        let result = [];\n        for (let effect of effects) {\n            let mapped = effect.map(mapping);\n            if (mapped)\n                result.push(mapped);\n        }\n        return result;\n    }\n}\n/**\nThis effect can be used to reconfigure the root extensions of\nthe editor. Doing this will discard any extensions\n[appended](https://codemirror.net/6/docs/ref/#state.StateEffect^appendConfig), but does not reset\nthe content of [reconfigured](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure)\ncompartments.\n*/\nStateEffect.reconfigure = /*@__PURE__*/StateEffect.define();\n/**\nAppend extensions to the top-level configuration of the editor.\n*/\nStateEffect.appendConfig = /*@__PURE__*/StateEffect.define();\n/**\nChanges to the editor state are grouped into transactions.\nTypically, a user action creates a single transaction, which may\ncontain any number of document changes, may change the selection,\nor have other effects. Create a transaction by calling\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update), or immediately\ndispatch one by calling\n[`EditorView.dispatch`](https://codemirror.net/6/docs/ref/#view.EditorView.dispatch).\n*/\nclass Transaction {\n    constructor(\n    /**\n    The state from which the transaction starts.\n    */\n    startState, \n    /**\n    The document changes made by this transaction.\n    */\n    changes, \n    /**\n    The selection set by this transaction, or undefined if it\n    doesn't explicitly set a selection.\n    */\n    selection, \n    /**\n    The effects added to the transaction.\n    */\n    effects, \n    /**\n    @internal\n    */\n    annotations, \n    /**\n    Whether the selection should be scrolled into view after this\n    transaction is dispatched.\n    */\n    scrollIntoView) {\n        this.startState = startState;\n        this.changes = changes;\n        this.selection = selection;\n        this.effects = effects;\n        this.annotations = annotations;\n        this.scrollIntoView = scrollIntoView;\n        /**\n        @internal\n        */\n        this._doc = null;\n        /**\n        @internal\n        */\n        this._state = null;\n        if (selection)\n            checkSelection(selection, changes.newLength);\n        if (!annotations.some((a) => a.type == Transaction.time))\n            this.annotations = annotations.concat(Transaction.time.of(Date.now()));\n    }\n    /**\n    @internal\n    */\n    static create(startState, changes, selection, effects, annotations, scrollIntoView) {\n        return new Transaction(startState, changes, selection, effects, annotations, scrollIntoView);\n    }\n    /**\n    The new document produced by the transaction. Contrary to\n    [`.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state)`.doc`, accessing this won't\n    force the entire new state to be computed right away, so it is\n    recommended that [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) use this getter\n    when they need to look at the new document.\n    */\n    get newDoc() {\n        return this._doc || (this._doc = this.changes.apply(this.startState.doc));\n    }\n    /**\n    The new selection produced by the transaction. If\n    [`this.selection`](https://codemirror.net/6/docs/ref/#state.Transaction.selection) is undefined,\n    this will [map](https://codemirror.net/6/docs/ref/#state.EditorSelection.map) the start state's\n    current selection through the changes made by the transaction.\n    */\n    get newSelection() {\n        return this.selection || this.startState.selection.map(this.changes);\n    }\n    /**\n    The new state created by the transaction. Computed on demand\n    (but retained for subsequent access), so it is recommended not to\n    access it in [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) when possible.\n    */\n    get state() {\n        if (!this._state)\n            this.startState.applyTransaction(this);\n        return this._state;\n    }\n    /**\n    Get the value of the given annotation type, if any.\n    */\n    annotation(type) {\n        for (let ann of this.annotations)\n            if (ann.type == type)\n                return ann.value;\n        return undefined;\n    }\n    /**\n    Indicates whether the transaction changed the document.\n    */\n    get docChanged() { return !this.changes.empty; }\n    /**\n    Indicates whether this transaction reconfigures the state\n    (through a [configuration compartment](https://codemirror.net/6/docs/ref/#state.Compartment) or\n    with a top-level configuration\n    [effect](https://codemirror.net/6/docs/ref/#state.StateEffect^reconfigure).\n    */\n    get reconfigured() { return this.startState.config != this.state.config; }\n    /**\n    Returns true if the transaction has a [user\n    event](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent) annotation that is equal to\n    or more specific than `event`. For example, if the transaction\n    has `\"select.pointer\"` as user event, `\"select\"` and\n    `\"select.pointer\"` will match it.\n    */\n    isUserEvent(event) {\n        let e = this.annotation(Transaction.userEvent);\n        return !!(e && (e == event || e.length > event.length && e.slice(0, event.length) == event && e[event.length] == \".\"));\n    }\n}\n/**\nAnnotation used to store transaction timestamps. Automatically\nadded to every transaction, holding `Date.now()`.\n*/\nTransaction.time = /*@__PURE__*/Annotation.define();\n/**\nAnnotation used to associate a transaction with a user interface\nevent. Holds a string identifying the event, using a\ndot-separated format to support attaching more specific\ninformation. The events used by the core libraries are:\n\n - `\"input\"` when content is entered\n   - `\"input.type\"` for typed input\n     - `\"input.type.compose\"` for composition\n   - `\"input.paste\"` for pasted input\n   - `\"input.drop\"` when adding content with drag-and-drop\n   - `\"input.complete\"` when autocompleting\n - `\"delete\"` when the user deletes content\n   - `\"delete.selection\"` when deleting the selection\n   - `\"delete.forward\"` when deleting forward from the selection\n   - `\"delete.backward\"` when deleting backward from the selection\n   - `\"delete.cut\"` when cutting to the clipboard\n - `\"move\"` when content is moved\n   - `\"move.drop\"` when content is moved within the editor through drag-and-drop\n - `\"select\"` when explicitly changing the selection\n   - `\"select.pointer\"` when selecting with a mouse or other pointing device\n - `\"undo\"` and `\"redo\"` for history actions\n\nUse [`isUserEvent`](https://codemirror.net/6/docs/ref/#state.Transaction.isUserEvent) to check\nwhether the annotation matches a given event.\n*/\nTransaction.userEvent = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating whether a transaction should be added to\nthe undo history or not.\n*/\nTransaction.addToHistory = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating (when present and true) that a transaction\nrepresents a change made by some other actor, not the user. This\nis used, for example, to tag other people's changes in\ncollaborative editing.\n*/\nTransaction.remote = /*@__PURE__*/Annotation.define();\nfunction joinRanges(a, b) {\n    let result = [];\n    for (let iA = 0, iB = 0;;) {\n        let from, to;\n        if (iA < a.length && (iB == b.length || b[iB] >= a[iA])) {\n            from = a[iA++];\n            to = a[iA++];\n        }\n        else if (iB < b.length) {\n            from = b[iB++];\n            to = b[iB++];\n        }\n        else\n            return result;\n        if (!result.length || result[result.length - 1] < from)\n            result.push(from, to);\n        else if (result[result.length - 1] < to)\n            result[result.length - 1] = to;\n    }\n}\nfunction mergeTransaction(a, b, sequential) {\n    var _a;\n    let mapForA, mapForB, changes;\n    if (sequential) {\n        mapForA = b.changes;\n        mapForB = ChangeSet.empty(b.changes.length);\n        changes = a.changes.compose(b.changes);\n    }\n    else {\n        mapForA = b.changes.map(a.changes);\n        mapForB = a.changes.mapDesc(b.changes, true);\n        changes = a.changes.compose(mapForA);\n    }\n    return {\n        changes,\n        selection: b.selection ? b.selection.map(mapForB) : (_a = a.selection) === null || _a === void 0 ? void 0 : _a.map(mapForA),\n        effects: StateEffect.mapEffects(a.effects, mapForA).concat(StateEffect.mapEffects(b.effects, mapForB)),\n        annotations: a.annotations.length ? a.annotations.concat(b.annotations) : b.annotations,\n        scrollIntoView: a.scrollIntoView || b.scrollIntoView\n    };\n}\nfunction resolveTransactionInner(state, spec, docSize) {\n    let sel = spec.selection, annotations = asArray(spec.annotations);\n    if (spec.userEvent)\n        annotations = annotations.concat(Transaction.userEvent.of(spec.userEvent));\n    return {\n        changes: spec.changes instanceof ChangeSet ? spec.changes\n            : ChangeSet.of(spec.changes || [], docSize, state.facet(lineSeparator)),\n        selection: sel && (sel instanceof EditorSelection ? sel : EditorSelection.single(sel.anchor, sel.head)),\n        effects: asArray(spec.effects),\n        annotations,\n        scrollIntoView: !!spec.scrollIntoView\n    };\n}\nfunction resolveTransaction(state, specs, filter) {\n    let s = resolveTransactionInner(state, specs.length ? specs[0] : {}, state.doc.length);\n    if (specs.length && specs[0].filter === false)\n        filter = false;\n    for (let i = 1; i < specs.length; i++) {\n        if (specs[i].filter === false)\n            filter = false;\n        let seq = !!specs[i].sequential;\n        s = mergeTransaction(s, resolveTransactionInner(state, specs[i], seq ? s.changes.newLength : state.doc.length), seq);\n    }\n    let tr = Transaction.create(state, s.changes, s.selection, s.effects, s.annotations, s.scrollIntoView);\n    return extendTransaction(filter ? filterTransaction(tr) : tr);\n}\n// Finish a transaction by applying filters if necessary.\nfunction filterTransaction(tr) {\n    let state = tr.startState;\n    // Change filters\n    let result = true;\n    for (let filter of state.facet(changeFilter)) {\n        let value = filter(tr);\n        if (value === false) {\n            result = false;\n            break;\n        }\n        if (Array.isArray(value))\n            result = result === true ? value : joinRanges(result, value);\n    }\n    if (result !== true) {\n        let changes, back;\n        if (result === false) {\n            back = tr.changes.invertedDesc;\n            changes = ChangeSet.empty(state.doc.length);\n        }\n        else {\n            let filtered = tr.changes.filter(result);\n            changes = filtered.changes;\n            back = filtered.filtered.mapDesc(filtered.changes).invertedDesc;\n        }\n        tr = Transaction.create(state, changes, tr.selection && tr.selection.map(back), StateEffect.mapEffects(tr.effects, back), tr.annotations, tr.scrollIntoView);\n    }\n    // Transaction filters\n    let filters = state.facet(transactionFilter);\n    for (let i = filters.length - 1; i >= 0; i--) {\n        let filtered = filters[i](tr);\n        if (filtered instanceof Transaction)\n            tr = filtered;\n        else if (Array.isArray(filtered) && filtered.length == 1 && filtered[0] instanceof Transaction)\n            tr = filtered[0];\n        else\n            tr = resolveTransaction(state, asArray(filtered), false);\n    }\n    return tr;\n}\nfunction extendTransaction(tr) {\n    let state = tr.startState, extenders = state.facet(transactionExtender), spec = tr;\n    for (let i = extenders.length - 1; i >= 0; i--) {\n        let extension = extenders[i](tr);\n        if (extension && Object.keys(extension).length)\n            spec = mergeTransaction(spec, resolveTransactionInner(state, extension, tr.changes.newLength), true);\n    }\n    return spec == tr ? tr : Transaction.create(state, tr.changes, tr.selection, spec.effects, spec.annotations, spec.scrollIntoView);\n}\nconst none = [];\nfunction asArray(value) {\n    return value == null ? none : Array.isArray(value) ? value : [value];\n}\n\n/**\nThe categories produced by a [character\ncategorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer). These are used\ndo things like selecting by word.\n*/\nvar CharCategory = /*@__PURE__*/(function (CharCategory) {\n    /**\n    Word characters.\n    */\n    CharCategory[CharCategory[\"Word\"] = 0] = \"Word\";\n    /**\n    Whitespace.\n    */\n    CharCategory[CharCategory[\"Space\"] = 1] = \"Space\";\n    /**\n    Anything else.\n    */\n    CharCategory[CharCategory[\"Other\"] = 2] = \"Other\";\nreturn CharCategory})(CharCategory || (CharCategory = {}));\nconst nonASCIISingleCaseWordChar = /[\\u00df\\u0587\\u0590-\\u05f4\\u0600-\\u06ff\\u3040-\\u309f\\u30a0-\\u30ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\uac00-\\ud7af]/;\nlet wordChar;\ntry {\n    wordChar = /*@__PURE__*/new RegExp(\"[\\\\p{Alphabetic}\\\\p{Number}_]\", \"u\");\n}\ncatch (_) { }\nfunction hasWordChar(str) {\n    if (wordChar)\n        return wordChar.test(str);\n    for (let i = 0; i < str.length; i++) {\n        let ch = str[i];\n        if (/\\w/.test(ch) || ch > \"\\x80\" && (ch.toUpperCase() != ch.toLowerCase() || nonASCIISingleCaseWordChar.test(ch)))\n            return true;\n    }\n    return false;\n}\nfunction makeCategorizer(wordChars) {\n    return (char) => {\n        if (!/\\S/.test(char))\n            return CharCategory.Space;\n        if (hasWordChar(char))\n            return CharCategory.Word;\n        for (let i = 0; i < wordChars.length; i++)\n            if (char.indexOf(wordChars[i]) > -1)\n                return CharCategory.Word;\n        return CharCategory.Other;\n    };\n}\n\n/**\nThe editor state class is a persistent (immutable) data structure.\nTo update a state, you [create](https://codemirror.net/6/docs/ref/#state.EditorState.update) a\n[transaction](https://codemirror.net/6/docs/ref/#state.Transaction), which produces a _new_ state\ninstance, without modifying the original object.\n\nAs such, _never_ mutate properties of a state directly. That'll\njust break things.\n*/\nclass EditorState {\n    constructor(\n    /**\n    @internal\n    */\n    config, \n    /**\n    The current document.\n    */\n    doc, \n    /**\n    The current selection.\n    */\n    selection, \n    /**\n    @internal\n    */\n    values, computeSlot, tr) {\n        this.config = config;\n        this.doc = doc;\n        this.selection = selection;\n        this.values = values;\n        this.status = config.statusTemplate.slice();\n        this.computeSlot = computeSlot;\n        // Fill in the computed state immediately, so that further queries\n        // for it made during the update return this state\n        if (tr)\n            tr._state = this;\n        for (let i = 0; i < this.config.dynamicSlots.length; i++)\n            ensureAddr(this, i << 1);\n        this.computeSlot = null;\n    }\n    field(field, require = true) {\n        let addr = this.config.address[field.id];\n        if (addr == null) {\n            if (require)\n                throw new RangeError(\"Field is not present in this state\");\n            return undefined;\n        }\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Create a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction) that updates this\n    state. Any number of [transaction specs](https://codemirror.net/6/docs/ref/#state.TransactionSpec)\n    can be passed. Unless\n    [`sequential`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.sequential) is set, the\n    [changes](https://codemirror.net/6/docs/ref/#state.TransactionSpec.changes) (if any) of each spec\n    are assumed to start in the _current_ document (not the document\n    produced by previous specs), and its\n    [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection) and\n    [effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) are assumed to refer\n    to the document created by its _own_ changes. The resulting\n    transaction contains the combined effect of all the different\n    specs. For [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection), later\n    specs take precedence over earlier ones.\n    */\n    update(...specs) {\n        return resolveTransaction(this, specs, true);\n    }\n    /**\n    @internal\n    */\n    applyTransaction(tr) {\n        let conf = this.config, { base, compartments } = conf;\n        for (let effect of tr.effects) {\n            if (effect.is(Compartment.reconfigure)) {\n                if (conf) {\n                    compartments = new Map;\n                    conf.compartments.forEach((val, key) => compartments.set(key, val));\n                    conf = null;\n                }\n                compartments.set(effect.value.compartment, effect.value.extension);\n            }\n            else if (effect.is(StateEffect.reconfigure)) {\n                conf = null;\n                base = effect.value;\n            }\n            else if (effect.is(StateEffect.appendConfig)) {\n                conf = null;\n                base = asArray(base).concat(effect.value);\n            }\n        }\n        let startValues;\n        if (!conf) {\n            conf = Configuration.resolve(base, compartments, this);\n            let intermediateState = new EditorState(conf, this.doc, this.selection, conf.dynamicSlots.map(() => null), (state, slot) => slot.reconfigure(state, this), null);\n            startValues = intermediateState.values;\n        }\n        else {\n            startValues = tr.startState.values.slice();\n        }\n        let selection = tr.startState.facet(allowMultipleSelections) ? tr.newSelection : tr.newSelection.asSingle();\n        new EditorState(conf, tr.newDoc, selection, startValues, (state, slot) => slot.update(state, tr), tr);\n    }\n    /**\n    Create a [transaction spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec) that\n    replaces every selection range with the given content.\n    */\n    replaceSelection(text) {\n        if (typeof text == \"string\")\n            text = this.toText(text);\n        return this.changeByRange(range => ({ changes: { from: range.from, to: range.to, insert: text },\n            range: EditorSelection.cursor(range.from + text.length) }));\n    }\n    /**\n    Create a set of changes and a new selection by running the given\n    function for each range in the active selection. The function\n    can return an optional set of changes (in the coordinate space\n    of the start document), plus an updated range (in the coordinate\n    space of the document produced by the call's own changes). This\n    method will merge all the changes and ranges into a single\n    changeset and selection, and return it as a [transaction\n    spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec), which can be passed to\n    [`update`](https://codemirror.net/6/docs/ref/#state.EditorState.update).\n    */\n    changeByRange(f) {\n        let sel = this.selection;\n        let result1 = f(sel.ranges[0]);\n        let changes = this.changes(result1.changes), ranges = [result1.range];\n        let effects = asArray(result1.effects);\n        for (let i = 1; i < sel.ranges.length; i++) {\n            let result = f(sel.ranges[i]);\n            let newChanges = this.changes(result.changes), newMapped = newChanges.map(changes);\n            for (let j = 0; j < i; j++)\n                ranges[j] = ranges[j].map(newMapped);\n            let mapBy = changes.mapDesc(newChanges, true);\n            ranges.push(result.range.map(mapBy));\n            changes = changes.compose(newMapped);\n            effects = StateEffect.mapEffects(effects, newMapped).concat(StateEffect.mapEffects(asArray(result.effects), mapBy));\n        }\n        return {\n            changes,\n            selection: EditorSelection.create(ranges, sel.mainIndex),\n            effects\n        };\n    }\n    /**\n    Create a [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet) from the given change\n    description, taking the state's document length and line\n    separator into account.\n    */\n    changes(spec = []) {\n        if (spec instanceof ChangeSet)\n            return spec;\n        return ChangeSet.of(spec, this.doc.length, this.facet(EditorState.lineSeparator));\n    }\n    /**\n    Using the state's [line\n    separator](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator), create a\n    [`Text`](https://codemirror.net/6/docs/ref/#state.Text) instance from the given string.\n    */\n    toText(string) {\n        return Text.of(string.split(this.facet(EditorState.lineSeparator) || DefaultSplit));\n    }\n    /**\n    Return the given range of the document as a string.\n    */\n    sliceDoc(from = 0, to = this.doc.length) {\n        return this.doc.sliceString(from, to, this.lineBreak);\n    }\n    /**\n    Get the value of a state [facet](https://codemirror.net/6/docs/ref/#state.Facet).\n    */\n    facet(facet) {\n        let addr = this.config.address[facet.id];\n        if (addr == null)\n            return facet.default;\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Convert this state to a JSON-serializable object. When custom\n    fields should be serialized, you can pass them in as an object\n    mapping property names (in the resulting object, which should\n    not use `doc` or `selection`) to fields.\n    */\n    toJSON(fields) {\n        let result = {\n            doc: this.sliceDoc(),\n            selection: this.selection.toJSON()\n        };\n        if (fields)\n            for (let prop in fields) {\n                let value = fields[prop];\n                if (value instanceof StateField && this.config.address[value.id] != null)\n                    result[prop] = value.spec.toJSON(this.field(fields[prop]), this);\n            }\n        return result;\n    }\n    /**\n    Deserialize a state from its JSON representation. When custom\n    fields should be deserialized, pass the same object you passed\n    to [`toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) when serializing as\n    third argument.\n    */\n    static fromJSON(json, config = {}, fields) {\n        if (!json || typeof json.doc != \"string\")\n            throw new RangeError(\"Invalid JSON representation for EditorState\");\n        let fieldInit = [];\n        if (fields)\n            for (let prop in fields) {\n                if (Object.prototype.hasOwnProperty.call(json, prop)) {\n                    let field = fields[prop], value = json[prop];\n                    fieldInit.push(field.init(state => field.spec.fromJSON(value, state)));\n                }\n            }\n        return EditorState.create({\n            doc: json.doc,\n            selection: EditorSelection.fromJSON(json.selection),\n            extensions: config.extensions ? fieldInit.concat([config.extensions]) : fieldInit\n        });\n    }\n    /**\n    Create a new state. You'll usually only need this when\n    initializing an editor—updated states are created by applying\n    transactions.\n    */\n    static create(config = {}) {\n        let configuration = Configuration.resolve(config.extensions || [], new Map);\n        let doc = config.doc instanceof Text ? config.doc\n            : Text.of((config.doc || \"\").split(configuration.staticFacet(EditorState.lineSeparator) || DefaultSplit));\n        let selection = !config.selection ? EditorSelection.single(0)\n            : config.selection instanceof EditorSelection ? config.selection\n                : EditorSelection.single(config.selection.anchor, config.selection.head);\n        checkSelection(selection, doc.length);\n        if (!configuration.staticFacet(allowMultipleSelections))\n            selection = selection.asSingle();\n        return new EditorState(configuration, doc, selection, configuration.dynamicSlots.map(() => null), (state, slot) => slot.create(state), null);\n    }\n    /**\n    The size (in columns) of a tab in the document, determined by\n    the [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) facet.\n    */\n    get tabSize() { return this.facet(EditorState.tabSize); }\n    /**\n    Get the proper [line-break](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator)\n    string for this state.\n    */\n    get lineBreak() { return this.facet(EditorState.lineSeparator) || \"\\n\"; }\n    /**\n    Returns true when the editor is\n    [configured](https://codemirror.net/6/docs/ref/#state.EditorState^readOnly) to be read-only.\n    */\n    get readOnly() { return this.facet(readOnly); }\n    /**\n    Look up a translation for the given phrase (via the\n    [`phrases`](https://codemirror.net/6/docs/ref/#state.EditorState^phrases) facet), or return the\n    original string if no translation is found.\n    \n    If additional arguments are passed, they will be inserted in\n    place of markers like `$1` (for the first value) and `$2`, etc.\n    A single `$` is equivalent to `$1`, and `$$` will produce a\n    literal dollar sign.\n    */\n    phrase(phrase, ...insert) {\n        for (let map of this.facet(EditorState.phrases))\n            if (Object.prototype.hasOwnProperty.call(map, phrase)) {\n                phrase = map[phrase];\n                break;\n            }\n        if (insert.length)\n            phrase = phrase.replace(/\\$(\\$|\\d*)/g, (m, i) => {\n                if (i == \"$\")\n                    return \"$\";\n                let n = +(i || 1);\n                return !n || n > insert.length ? m : insert[n - 1];\n            });\n        return phrase;\n    }\n    /**\n    Find the values for a given language data field, provided by the\n    the [`languageData`](https://codemirror.net/6/docs/ref/#state.EditorState^languageData) facet.\n    \n    Examples of language data fields are...\n    \n    - [`\"commentTokens\"`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) for specifying\n      comment syntax.\n    - [`\"autocomplete\"`](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion^config.override)\n      for providing language-specific completion sources.\n    - [`\"wordChars\"`](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer) for adding\n      characters that should be considered part of words in this\n      language.\n    - [`\"closeBrackets\"`](https://codemirror.net/6/docs/ref/#autocomplete.CloseBracketConfig) controls\n      bracket closing behavior.\n    */\n    languageDataAt(name, pos, side = -1) {\n        let values = [];\n        for (let provider of this.facet(languageData)) {\n            for (let result of provider(this, pos, side)) {\n                if (Object.prototype.hasOwnProperty.call(result, name))\n                    values.push(result[name]);\n            }\n        }\n        return values;\n    }\n    /**\n    Return a function that can categorize strings (expected to\n    represent a single [grapheme cluster](https://codemirror.net/6/docs/ref/#state.findClusterBreak))\n    into one of:\n    \n     - Word (contains an alphanumeric character or a character\n       explicitly listed in the local language's `\"wordChars\"`\n       language data, which should be a string)\n     - Space (contains only whitespace)\n     - Other (anything else)\n    */\n    charCategorizer(at) {\n        return makeCategorizer(this.languageDataAt(\"wordChars\", at).join(\"\"));\n    }\n    /**\n    Find the word at the given position, meaning the range\n    containing all [word](https://codemirror.net/6/docs/ref/#state.CharCategory.Word) characters\n    around it. If no word characters are adjacent to the position,\n    this returns null.\n    */\n    wordAt(pos) {\n        let { text, from, length } = this.doc.lineAt(pos);\n        let cat = this.charCategorizer(pos);\n        let start = pos - from, end = pos - from;\n        while (start > 0) {\n            let prev = findClusterBreak(text, start, false);\n            if (cat(text.slice(prev, start)) != CharCategory.Word)\n                break;\n            start = prev;\n        }\n        while (end < length) {\n            let next = findClusterBreak(text, end);\n            if (cat(text.slice(end, next)) != CharCategory.Word)\n                break;\n            end = next;\n        }\n        return start == end ? null : EditorSelection.range(start + from, end + from);\n    }\n}\n/**\nA facet that, when enabled, causes the editor to allow multiple\nranges to be selected. Be careful though, because by default the\neditor relies on the native DOM selection, which cannot handle\nmultiple selections. An extension like\n[`drawSelection`](https://codemirror.net/6/docs/ref/#view.drawSelection) can be used to make\nsecondary selections visible to the user.\n*/\nEditorState.allowMultipleSelections = allowMultipleSelections;\n/**\nConfigures the tab size to use in this state. The first\n(highest-precedence) value of the facet is used. If no value is\ngiven, this defaults to 4.\n*/\nEditorState.tabSize = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : 4\n});\n/**\nThe line separator to use. By default, any of `\"\\n\"`, `\"\\r\\n\"`\nand `\"\\r\"` is treated as a separator when splitting lines, and\nlines are joined with `\"\\n\"`.\n\nWhen you configure a value here, only that precise separator\nwill be used, allowing you to round-trip documents through the\neditor without normalizing line separators.\n*/\nEditorState.lineSeparator = lineSeparator;\n/**\nThis facet controls the value of the\n[`readOnly`](https://codemirror.net/6/docs/ref/#state.EditorState.readOnly) getter, which is\nconsulted by commands and extensions that implement editing\nfunctionality to determine whether they should apply. It\ndefaults to false, but when its highest-precedence value is\n`true`, such functionality disables itself.\n\nNot to be confused with\n[`EditorView.editable`](https://codemirror.net/6/docs/ref/#view.EditorView^editable), which\ncontrols whether the editor's DOM is set to be editable (and\nthus focusable).\n*/\nEditorState.readOnly = readOnly;\n/**\nRegisters translation phrases. The\n[`phrase`](https://codemirror.net/6/docs/ref/#state.EditorState.phrase) method will look through\nall objects registered with this facet to find translations for\nits argument.\n*/\nEditorState.phrases = /*@__PURE__*/Facet.define({\n    compare(a, b) {\n        let kA = Object.keys(a), kB = Object.keys(b);\n        return kA.length == kB.length && kA.every(k => a[k] == b[k]);\n    }\n});\n/**\nA facet used to register [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) providers.\n*/\nEditorState.languageData = languageData;\n/**\nFacet used to register change filters, which are called for each\ntransaction (unless explicitly\n[disabled](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter)), and can suppress\npart of the transaction's changes.\n\nSuch a function can return `true` to indicate that it doesn't\nwant to do anything, `false` to completely stop the changes in\nthe transaction, or a set of ranges in which changes should be\nsuppressed. Such ranges are represented as an array of numbers,\nwith each pair of two numbers indicating the start and end of a\nrange. So for example `[10, 20, 100, 110]` suppresses changes\nbetween 10 and 20, and between 100 and 110.\n*/\nEditorState.changeFilter = changeFilter;\n/**\nFacet used to register a hook that gets a chance to update or\nreplace transaction specs before they are applied. This will\nonly be applied for transactions that don't have\n[`filter`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter) set to `false`. You\ncan either return a single transaction spec (possibly the input\ntransaction), or an array of specs (which will be combined in\nthe same way as the arguments to\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update)).\n\nWhen possible, it is recommended to avoid accessing\n[`Transaction.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state) in a filter,\nsince it will force creation of a state that will then be\ndiscarded again, if the transaction is actually filtered.\n\n(This functionality should be used with care. Indiscriminately\nmodifying transaction is likely to break something or degrade\nthe user experience.)\n*/\nEditorState.transactionFilter = transactionFilter;\n/**\nThis is a more limited form of\n[`transactionFilter`](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter),\nwhich can only add\n[annotations](https://codemirror.net/6/docs/ref/#state.TransactionSpec.annotations) and\n[effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects). _But_, this type\nof filter runs even if the transaction has disabled regular\n[filtering](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter), making it suitable\nfor effects that don't need to touch the changes or selection,\nbut do want to process every transaction.\n\nExtenders run _after_ filters, when both are present.\n*/\nEditorState.transactionExtender = transactionExtender;\nCompartment.reconfigure = /*@__PURE__*/StateEffect.define();\n\n/**\nUtility function for combining behaviors to fill in a config\nobject from an array of provided configs. `defaults` should hold\ndefault values for all optional fields in `Config`.\n\nThe function will, by default, error\nwhen a field gets two values that aren't `===`-equal, but you can\nprovide combine functions per field to do something else.\n*/\nfunction combineConfig(configs, defaults, // Should hold only the optional properties of Config, but I haven't managed to express that\ncombine = {}) {\n    let result = {};\n    for (let config of configs)\n        for (let key of Object.keys(config)) {\n            let value = config[key], current = result[key];\n            if (current === undefined)\n                result[key] = value;\n            else if (current === value || value === undefined) ; // No conflict\n            else if (Object.hasOwnProperty.call(combine, key))\n                result[key] = combine[key](current, value);\n            else\n                throw new Error(\"Config merge conflict for field \" + key);\n        }\n    for (let key in defaults)\n        if (result[key] === undefined)\n            result[key] = defaults[key];\n    return result;\n}\n\n/**\nEach range is associated with a value, which must inherit from\nthis class.\n*/\nclass RangeValue {\n    /**\n    Compare this value with another value. Used when comparing\n    rangesets. The default implementation compares by identity.\n    Unless you are only creating a fixed number of unique instances\n    of your value type, it is a good idea to implement this\n    properly.\n    */\n    eq(other) { return this == other; }\n    /**\n    Create a [range](https://codemirror.net/6/docs/ref/#state.Range) with this value.\n    */\n    range(from, to = from) { return Range.create(from, to, this); }\n}\nRangeValue.prototype.startSide = RangeValue.prototype.endSide = 0;\nRangeValue.prototype.point = false;\nRangeValue.prototype.mapMode = MapMode.TrackDel;\n/**\nA range associates a value with a range of positions.\n*/\nclass Range {\n    constructor(\n    /**\n    The range's start position.\n    */\n    from, \n    /**\n    Its end position.\n    */\n    to, \n    /**\n    The value associated with this range.\n    */\n    value) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n    }\n    /**\n    @internal\n    */\n    static create(from, to, value) {\n        return new Range(from, to, value);\n    }\n}\nfunction cmpRange(a, b) {\n    return a.from - b.from || a.value.startSide - b.value.startSide;\n}\nclass Chunk {\n    constructor(from, to, value, \n    // Chunks are marked with the largest point that occurs\n    // in them (or -1 for no points), so that scans that are\n    // only interested in points (such as the\n    // heightmap-related logic) can skip range-only chunks.\n    maxPoint) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n        this.maxPoint = maxPoint;\n    }\n    get length() { return this.to[this.to.length - 1]; }\n    // Find the index of the given position and side. Use the ranges'\n    // `from` pos when `end == false`, `to` when `end == true`.\n    findIndex(pos, side, end, startAt = 0) {\n        let arr = end ? this.to : this.from;\n        for (let lo = startAt, hi = arr.length;;) {\n            if (lo == hi)\n                return lo;\n            let mid = (lo + hi) >> 1;\n            let diff = arr[mid] - pos || (end ? this.value[mid].endSide : this.value[mid].startSide) - side;\n            if (mid == lo)\n                return diff >= 0 ? lo : hi;\n            if (diff >= 0)\n                hi = mid;\n            else\n                lo = mid + 1;\n        }\n    }\n    between(offset, from, to, f) {\n        for (let i = this.findIndex(from, -********** /* C.Far */, true), e = this.findIndex(to, ********** /* C.Far */, false, i); i < e; i++)\n            if (f(this.from[i] + offset, this.to[i] + offset, this.value[i]) === false)\n                return false;\n    }\n    map(offset, changes) {\n        let value = [], from = [], to = [], newPos = -1, maxPoint = -1;\n        for (let i = 0; i < this.value.length; i++) {\n            let val = this.value[i], curFrom = this.from[i] + offset, curTo = this.to[i] + offset, newFrom, newTo;\n            if (curFrom == curTo) {\n                let mapped = changes.mapPos(curFrom, val.startSide, val.mapMode);\n                if (mapped == null)\n                    continue;\n                newFrom = newTo = mapped;\n                if (val.startSide != val.endSide) {\n                    newTo = changes.mapPos(curFrom, val.endSide);\n                    if (newTo < newFrom)\n                        continue;\n                }\n            }\n            else {\n                newFrom = changes.mapPos(curFrom, val.startSide);\n                newTo = changes.mapPos(curTo, val.endSide);\n                if (newFrom > newTo || newFrom == newTo && val.startSide > 0 && val.endSide <= 0)\n                    continue;\n            }\n            if ((newTo - newFrom || val.endSide - val.startSide) < 0)\n                continue;\n            if (newPos < 0)\n                newPos = newFrom;\n            if (val.point)\n                maxPoint = Math.max(maxPoint, newTo - newFrom);\n            value.push(val);\n            from.push(newFrom - newPos);\n            to.push(newTo - newPos);\n        }\n        return { mapped: value.length ? new Chunk(from, to, value, maxPoint) : null, pos: newPos };\n    }\n}\n/**\nA range set stores a collection of [ranges](https://codemirror.net/6/docs/ref/#state.Range) in a\nway that makes them efficient to [map](https://codemirror.net/6/docs/ref/#state.RangeSet.map) and\n[update](https://codemirror.net/6/docs/ref/#state.RangeSet.update). This is an immutable data\nstructure.\n*/\nclass RangeSet {\n    constructor(\n    /**\n    @internal\n    */\n    chunkPos, \n    /**\n    @internal\n    */\n    chunk, \n    /**\n    @internal\n    */\n    nextLayer, \n    /**\n    @internal\n    */\n    maxPoint) {\n        this.chunkPos = chunkPos;\n        this.chunk = chunk;\n        this.nextLayer = nextLayer;\n        this.maxPoint = maxPoint;\n    }\n    /**\n    @internal\n    */\n    static create(chunkPos, chunk, nextLayer, maxPoint) {\n        return new RangeSet(chunkPos, chunk, nextLayer, maxPoint);\n    }\n    /**\n    @internal\n    */\n    get length() {\n        let last = this.chunk.length - 1;\n        return last < 0 ? 0 : Math.max(this.chunkEnd(last), this.nextLayer.length);\n    }\n    /**\n    The number of ranges in the set.\n    */\n    get size() {\n        if (this.isEmpty)\n            return 0;\n        let size = this.nextLayer.size;\n        for (let chunk of this.chunk)\n            size += chunk.value.length;\n        return size;\n    }\n    /**\n    @internal\n    */\n    chunkEnd(index) {\n        return this.chunkPos[index] + this.chunk[index].length;\n    }\n    /**\n    Update the range set, optionally adding new ranges or filtering\n    out existing ones.\n    \n    (Note: The type parameter is just there as a kludge to work\n    around TypeScript variance issues that prevented `RangeSet<X>`\n    from being a subtype of `RangeSet<Y>` when `X` is a subtype of\n    `Y`.)\n    */\n    update(updateSpec) {\n        let { add = [], sort = false, filterFrom = 0, filterTo = this.length } = updateSpec;\n        let filter = updateSpec.filter;\n        if (add.length == 0 && !filter)\n            return this;\n        if (sort)\n            add = add.slice().sort(cmpRange);\n        if (this.isEmpty)\n            return add.length ? RangeSet.of(add) : this;\n        let cur = new LayerCursor(this, null, -1).goto(0), i = 0, spill = [];\n        let builder = new RangeSetBuilder();\n        while (cur.value || i < add.length) {\n            if (i < add.length && (cur.from - add[i].from || cur.startSide - add[i].value.startSide) >= 0) {\n                let range = add[i++];\n                if (!builder.addInner(range.from, range.to, range.value))\n                    spill.push(range);\n            }\n            else if (cur.rangeIndex == 1 && cur.chunkIndex < this.chunk.length &&\n                (i == add.length || this.chunkEnd(cur.chunkIndex) < add[i].from) &&\n                (!filter || filterFrom > this.chunkEnd(cur.chunkIndex) || filterTo < this.chunkPos[cur.chunkIndex]) &&\n                builder.addChunk(this.chunkPos[cur.chunkIndex], this.chunk[cur.chunkIndex])) {\n                cur.nextChunk();\n            }\n            else {\n                if (!filter || filterFrom > cur.to || filterTo < cur.from || filter(cur.from, cur.to, cur.value)) {\n                    if (!builder.addInner(cur.from, cur.to, cur.value))\n                        spill.push(Range.create(cur.from, cur.to, cur.value));\n                }\n                cur.next();\n            }\n        }\n        return builder.finishInner(this.nextLayer.isEmpty && !spill.length ? RangeSet.empty\n            : this.nextLayer.update({ add: spill, filter, filterFrom, filterTo }));\n    }\n    /**\n    Map this range set through a set of changes, return the new set.\n    */\n    map(changes) {\n        if (changes.empty || this.isEmpty)\n            return this;\n        let chunks = [], chunkPos = [], maxPoint = -1;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            let touch = changes.touchesRange(start, start + chunk.length);\n            if (touch === false) {\n                maxPoint = Math.max(maxPoint, chunk.maxPoint);\n                chunks.push(chunk);\n                chunkPos.push(changes.mapPos(start));\n            }\n            else if (touch === true) {\n                let { mapped, pos } = chunk.map(start, changes);\n                if (mapped) {\n                    maxPoint = Math.max(maxPoint, mapped.maxPoint);\n                    chunks.push(mapped);\n                    chunkPos.push(pos);\n                }\n            }\n        }\n        let next = this.nextLayer.map(changes);\n        return chunks.length == 0 ? next : new RangeSet(chunkPos, chunks, next || RangeSet.empty, maxPoint);\n    }\n    /**\n    Iterate over the ranges that touch the region `from` to `to`,\n    calling `f` for each. There is no guarantee that the ranges will\n    be reported in any specific order. When the callback returns\n    `false`, iteration stops.\n    */\n    between(from, to, f) {\n        if (this.isEmpty)\n            return;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            if (to >= start && from <= start + chunk.length &&\n                chunk.between(start, from - start, to - start, f) === false)\n                return;\n        }\n        this.nextLayer.between(from, to, f);\n    }\n    /**\n    Iterate over the ranges in this set, in order, including all\n    ranges that end at or after `from`.\n    */\n    iter(from = 0) {\n        return HeapCursor.from([this]).goto(from);\n    }\n    /**\n    @internal\n    */\n    get isEmpty() { return this.nextLayer == this; }\n    /**\n    Iterate over the ranges in a collection of sets, in order,\n    starting from `from`.\n    */\n    static iter(sets, from = 0) {\n        return HeapCursor.from(sets).goto(from);\n    }\n    /**\n    Iterate over two groups of sets, calling methods on `comparator`\n    to notify it of possible differences.\n    */\n    static compare(oldSets, newSets, \n    /**\n    This indicates how the underlying data changed between these\n    ranges, and is needed to synchronize the iteration.\n    */\n    textDiff, comparator, \n    /**\n    Can be used to ignore all non-point ranges, and points below\n    the given size. When -1, all ranges are compared.\n    */\n    minPointSize = -1) {\n        let a = oldSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let b = newSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let sharedChunks = findSharedChunks(a, b, textDiff);\n        let sideA = new SpanCursor(a, sharedChunks, minPointSize);\n        let sideB = new SpanCursor(b, sharedChunks, minPointSize);\n        textDiff.iterGaps((fromA, fromB, length) => compare(sideA, fromA, sideB, fromB, length, comparator));\n        if (textDiff.empty && textDiff.length == 0)\n            compare(sideA, 0, sideB, 0, 0, comparator);\n    }\n    /**\n    Compare the contents of two groups of range sets, returning true\n    if they are equivalent in the given range.\n    */\n    static eq(oldSets, newSets, from = 0, to) {\n        if (to == null)\n            to = ********** /* C.Far */ - 1;\n        let a = oldSets.filter(set => !set.isEmpty && newSets.indexOf(set) < 0);\n        let b = newSets.filter(set => !set.isEmpty && oldSets.indexOf(set) < 0);\n        if (a.length != b.length)\n            return false;\n        if (!a.length)\n            return true;\n        let sharedChunks = findSharedChunks(a, b);\n        let sideA = new SpanCursor(a, sharedChunks, 0).goto(from), sideB = new SpanCursor(b, sharedChunks, 0).goto(from);\n        for (;;) {\n            if (sideA.to != sideB.to ||\n                !sameValues(sideA.active, sideB.active) ||\n                sideA.point && (!sideB.point || !sideA.point.eq(sideB.point)))\n                return false;\n            if (sideA.to > to)\n                return true;\n            sideA.next();\n            sideB.next();\n        }\n    }\n    /**\n    Iterate over a group of range sets at the same time, notifying\n    the iterator about the ranges covering every given piece of\n    content. Returns the open count (see\n    [`SpanIterator.span`](https://codemirror.net/6/docs/ref/#state.SpanIterator.span)) at the end\n    of the iteration.\n    */\n    static spans(sets, from, to, iterator, \n    /**\n    When given and greater than -1, only points of at least this\n    size are taken into account.\n    */\n    minPointSize = -1) {\n        let cursor = new SpanCursor(sets, null, minPointSize).goto(from), pos = from;\n        let openRanges = cursor.openStart;\n        for (;;) {\n            let curTo = Math.min(cursor.to, to);\n            if (cursor.point) {\n                let active = cursor.activeForPoint(cursor.to);\n                let openCount = cursor.pointFrom < from ? active.length + 1\n                    : cursor.point.startSide < 0 ? active.length\n                        : Math.min(active.length, openRanges);\n                iterator.point(pos, curTo, cursor.point, active, openCount, cursor.pointRank);\n                openRanges = Math.min(cursor.openEnd(curTo), active.length);\n            }\n            else if (curTo > pos) {\n                iterator.span(pos, curTo, cursor.active, openRanges);\n                openRanges = cursor.openEnd(curTo);\n            }\n            if (cursor.to > to)\n                return openRanges + (cursor.point && cursor.to > to ? 1 : 0);\n            pos = cursor.to;\n            cursor.next();\n        }\n    }\n    /**\n    Create a range set for the given range or array of ranges. By\n    default, this expects the ranges to be _sorted_ (by start\n    position and, if two start at the same position,\n    `value.startSide`). You can pass `true` as second argument to\n    cause the method to sort them.\n    */\n    static of(ranges, sort = false) {\n        let build = new RangeSetBuilder();\n        for (let range of ranges instanceof Range ? [ranges] : sort ? lazySort(ranges) : ranges)\n            build.add(range.from, range.to, range.value);\n        return build.finish();\n    }\n    /**\n    Join an array of range sets into a single set.\n    */\n    static join(sets) {\n        if (!sets.length)\n            return RangeSet.empty;\n        let result = sets[sets.length - 1];\n        for (let i = sets.length - 2; i >= 0; i--) {\n            for (let layer = sets[i]; layer != RangeSet.empty; layer = layer.nextLayer)\n                result = new RangeSet(layer.chunkPos, layer.chunk, result, Math.max(layer.maxPoint, result.maxPoint));\n        }\n        return result;\n    }\n}\n/**\nThe empty set of ranges.\n*/\nRangeSet.empty = /*@__PURE__*/new RangeSet([], [], null, -1);\nfunction lazySort(ranges) {\n    if (ranges.length > 1)\n        for (let prev = ranges[0], i = 1; i < ranges.length; i++) {\n            let cur = ranges[i];\n            if (cmpRange(prev, cur) > 0)\n                return ranges.slice().sort(cmpRange);\n            prev = cur;\n        }\n    return ranges;\n}\nRangeSet.empty.nextLayer = RangeSet.empty;\n/**\nA range set builder is a data structure that helps build up a\n[range set](https://codemirror.net/6/docs/ref/#state.RangeSet) directly, without first allocating\nan array of [`Range`](https://codemirror.net/6/docs/ref/#state.Range) objects.\n*/\nclass RangeSetBuilder {\n    finishChunk(newArrays) {\n        this.chunks.push(new Chunk(this.from, this.to, this.value, this.maxPoint));\n        this.chunkPos.push(this.chunkStart);\n        this.chunkStart = -1;\n        this.setMaxPoint = Math.max(this.setMaxPoint, this.maxPoint);\n        this.maxPoint = -1;\n        if (newArrays) {\n            this.from = [];\n            this.to = [];\n            this.value = [];\n        }\n    }\n    /**\n    Create an empty builder.\n    */\n    constructor() {\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunkStart = -1;\n        this.last = null;\n        this.lastFrom = -********** /* C.Far */;\n        this.lastTo = -********** /* C.Far */;\n        this.from = [];\n        this.to = [];\n        this.value = [];\n        this.maxPoint = -1;\n        this.setMaxPoint = -1;\n        this.nextLayer = null;\n    }\n    /**\n    Add a range. Ranges should be added in sorted (by `from` and\n    `value.startSide`) order.\n    */\n    add(from, to, value) {\n        if (!this.addInner(from, to, value))\n            (this.nextLayer || (this.nextLayer = new RangeSetBuilder)).add(from, to, value);\n    }\n    /**\n    @internal\n    */\n    addInner(from, to, value) {\n        let diff = from - this.lastTo || value.startSide - this.last.endSide;\n        if (diff <= 0 && (from - this.lastFrom || value.startSide - this.last.startSide) < 0)\n            throw new Error(\"Ranges must be added sorted by `from` position and `startSide`\");\n        if (diff < 0)\n            return false;\n        if (this.from.length == 250 /* C.ChunkSize */)\n            this.finishChunk(true);\n        if (this.chunkStart < 0)\n            this.chunkStart = from;\n        this.from.push(from - this.chunkStart);\n        this.to.push(to - this.chunkStart);\n        this.last = value;\n        this.lastFrom = from;\n        this.lastTo = to;\n        this.value.push(value);\n        if (value.point)\n            this.maxPoint = Math.max(this.maxPoint, to - from);\n        return true;\n    }\n    /**\n    @internal\n    */\n    addChunk(from, chunk) {\n        if ((from - this.lastTo || chunk.value[0].startSide - this.last.endSide) < 0)\n            return false;\n        if (this.from.length)\n            this.finishChunk(true);\n        this.setMaxPoint = Math.max(this.setMaxPoint, chunk.maxPoint);\n        this.chunks.push(chunk);\n        this.chunkPos.push(from);\n        let last = chunk.value.length - 1;\n        this.last = chunk.value[last];\n        this.lastFrom = chunk.from[last] + from;\n        this.lastTo = chunk.to[last] + from;\n        return true;\n    }\n    /**\n    Finish the range set. Returns the new set. The builder can't be\n    used anymore after this has been called.\n    */\n    finish() { return this.finishInner(RangeSet.empty); }\n    /**\n    @internal\n    */\n    finishInner(next) {\n        if (this.from.length)\n            this.finishChunk(false);\n        if (this.chunks.length == 0)\n            return next;\n        let result = RangeSet.create(this.chunkPos, this.chunks, this.nextLayer ? this.nextLayer.finishInner(next) : next, this.setMaxPoint);\n        this.from = null; // Make sure further `add` calls produce errors\n        return result;\n    }\n}\nfunction findSharedChunks(a, b, textDiff) {\n    let inA = new Map();\n    for (let set of a)\n        for (let i = 0; i < set.chunk.length; i++)\n            if (set.chunk[i].maxPoint <= 0)\n                inA.set(set.chunk[i], set.chunkPos[i]);\n    let shared = new Set();\n    for (let set of b)\n        for (let i = 0; i < set.chunk.length; i++) {\n            let known = inA.get(set.chunk[i]);\n            if (known != null && (textDiff ? textDiff.mapPos(known) : known) == set.chunkPos[i] &&\n                !(textDiff === null || textDiff === void 0 ? void 0 : textDiff.touchesRange(known, known + set.chunk[i].length)))\n                shared.add(set.chunk[i]);\n        }\n    return shared;\n}\nclass LayerCursor {\n    constructor(layer, skip, minPoint, rank = 0) {\n        this.layer = layer;\n        this.skip = skip;\n        this.minPoint = minPoint;\n        this.rank = rank;\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    get endSide() { return this.value ? this.value.endSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        this.chunkIndex = this.rangeIndex = 0;\n        this.gotoInner(pos, side, false);\n        return this;\n    }\n    gotoInner(pos, side, forward) {\n        while (this.chunkIndex < this.layer.chunk.length) {\n            let next = this.layer.chunk[this.chunkIndex];\n            if (!(this.skip && this.skip.has(next) ||\n                this.layer.chunkEnd(this.chunkIndex) < pos ||\n                next.maxPoint < this.minPoint))\n                break;\n            this.chunkIndex++;\n            forward = false;\n        }\n        if (this.chunkIndex < this.layer.chunk.length) {\n            let rangeIndex = this.layer.chunk[this.chunkIndex].findIndex(pos - this.layer.chunkPos[this.chunkIndex], side, true);\n            if (!forward || this.rangeIndex < rangeIndex)\n                this.setRangeIndex(rangeIndex);\n        }\n        this.next();\n    }\n    forward(pos, side) {\n        if ((this.to - pos || this.endSide - side) < 0)\n            this.gotoInner(pos, side, true);\n    }\n    next() {\n        for (;;) {\n            if (this.chunkIndex == this.layer.chunk.length) {\n                this.from = this.to = ********** /* C.Far */;\n                this.value = null;\n                break;\n            }\n            else {\n                let chunkPos = this.layer.chunkPos[this.chunkIndex], chunk = this.layer.chunk[this.chunkIndex];\n                let from = chunkPos + chunk.from[this.rangeIndex];\n                this.from = from;\n                this.to = chunkPos + chunk.to[this.rangeIndex];\n                this.value = chunk.value[this.rangeIndex];\n                this.setRangeIndex(this.rangeIndex + 1);\n                if (this.minPoint < 0 || this.value.point && this.to - this.from >= this.minPoint)\n                    break;\n            }\n        }\n    }\n    setRangeIndex(index) {\n        if (index == this.layer.chunk[this.chunkIndex].value.length) {\n            this.chunkIndex++;\n            if (this.skip) {\n                while (this.chunkIndex < this.layer.chunk.length && this.skip.has(this.layer.chunk[this.chunkIndex]))\n                    this.chunkIndex++;\n            }\n            this.rangeIndex = 0;\n        }\n        else {\n            this.rangeIndex = index;\n        }\n    }\n    nextChunk() {\n        this.chunkIndex++;\n        this.rangeIndex = 0;\n        this.next();\n    }\n    compare(other) {\n        return this.from - other.from || this.startSide - other.startSide || this.rank - other.rank ||\n            this.to - other.to || this.endSide - other.endSide;\n    }\n}\nclass HeapCursor {\n    constructor(heap) {\n        this.heap = heap;\n    }\n    static from(sets, skip = null, minPoint = -1) {\n        let heap = [];\n        for (let i = 0; i < sets.length; i++) {\n            for (let cur = sets[i]; !cur.isEmpty; cur = cur.nextLayer) {\n                if (cur.maxPoint >= minPoint)\n                    heap.push(new LayerCursor(cur, skip, minPoint, i));\n            }\n        }\n        return heap.length == 1 ? heap[0] : new HeapCursor(heap);\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        for (let cur of this.heap)\n            cur.goto(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        for (let cur of this.heap)\n            cur.forward(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        if ((this.to - pos || this.value.endSide - side) < 0)\n            this.next();\n    }\n    next() {\n        if (this.heap.length == 0) {\n            this.from = this.to = ********** /* C.Far */;\n            this.value = null;\n            this.rank = -1;\n        }\n        else {\n            let top = this.heap[0];\n            this.from = top.from;\n            this.to = top.to;\n            this.value = top.value;\n            this.rank = top.rank;\n            if (top.value)\n                top.next();\n            heapBubble(this.heap, 0);\n        }\n    }\n}\nfunction heapBubble(heap, index) {\n    for (let cur = heap[index];;) {\n        let childIndex = (index << 1) + 1;\n        if (childIndex >= heap.length)\n            break;\n        let child = heap[childIndex];\n        if (childIndex + 1 < heap.length && child.compare(heap[childIndex + 1]) >= 0) {\n            child = heap[childIndex + 1];\n            childIndex++;\n        }\n        if (cur.compare(child) < 0)\n            break;\n        heap[childIndex] = cur;\n        heap[index] = child;\n        index = childIndex;\n    }\n}\nclass SpanCursor {\n    constructor(sets, skip, minPoint) {\n        this.minPoint = minPoint;\n        this.active = [];\n        this.activeTo = [];\n        this.activeRank = [];\n        this.minActive = -1;\n        // A currently active point range, if any\n        this.point = null;\n        this.pointFrom = 0;\n        this.pointRank = 0;\n        this.to = -********** /* C.Far */;\n        this.endSide = 0;\n        // The amount of open active ranges at the start of the iterator.\n        // Not including points.\n        this.openStart = -1;\n        this.cursor = HeapCursor.from(sets, skip, minPoint);\n    }\n    goto(pos, side = -********** /* C.Far */) {\n        this.cursor.goto(pos, side);\n        this.active.length = this.activeTo.length = this.activeRank.length = 0;\n        this.minActive = -1;\n        this.to = pos;\n        this.endSide = side;\n        this.openStart = -1;\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        while (this.minActive > -1 && (this.activeTo[this.minActive] - pos || this.active[this.minActive].endSide - side) < 0)\n            this.removeActive(this.minActive);\n        this.cursor.forward(pos, side);\n    }\n    removeActive(index) {\n        remove(this.active, index);\n        remove(this.activeTo, index);\n        remove(this.activeRank, index);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    addActive(trackOpen) {\n        let i = 0, { value, to, rank } = this.cursor;\n        // Organize active marks by rank first, then by size\n        while (i < this.activeRank.length && (rank - this.activeRank[i] || to - this.activeTo[i]) > 0)\n            i++;\n        insert(this.active, i, value);\n        insert(this.activeTo, i, to);\n        insert(this.activeRank, i, rank);\n        if (trackOpen)\n            insert(trackOpen, i, this.cursor.from);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    // After calling this, if `this.point` != null, the next range is a\n    // point. Otherwise, it's a regular range, covered by `this.active`.\n    next() {\n        let from = this.to, wasPoint = this.point;\n        this.point = null;\n        let trackOpen = this.openStart < 0 ? [] : null;\n        for (;;) {\n            let a = this.minActive;\n            if (a > -1 && (this.activeTo[a] - this.cursor.from || this.active[a].endSide - this.cursor.startSide) < 0) {\n                if (this.activeTo[a] > from) {\n                    this.to = this.activeTo[a];\n                    this.endSide = this.active[a].endSide;\n                    break;\n                }\n                this.removeActive(a);\n                if (trackOpen)\n                    remove(trackOpen, a);\n            }\n            else if (!this.cursor.value) {\n                this.to = this.endSide = ********** /* C.Far */;\n                break;\n            }\n            else if (this.cursor.from > from) {\n                this.to = this.cursor.from;\n                this.endSide = this.cursor.startSide;\n                break;\n            }\n            else {\n                let nextVal = this.cursor.value;\n                if (!nextVal.point) { // Opening a range\n                    this.addActive(trackOpen);\n                    this.cursor.next();\n                }\n                else if (wasPoint && this.cursor.to == this.to && this.cursor.from < this.cursor.to) {\n                    // Ignore any non-empty points that end precisely at the end of the prev point\n                    this.cursor.next();\n                }\n                else { // New point\n                    this.point = nextVal;\n                    this.pointFrom = this.cursor.from;\n                    this.pointRank = this.cursor.rank;\n                    this.to = this.cursor.to;\n                    this.endSide = nextVal.endSide;\n                    this.cursor.next();\n                    this.forward(this.to, this.endSide);\n                    break;\n                }\n            }\n        }\n        if (trackOpen) {\n            this.openStart = 0;\n            for (let i = trackOpen.length - 1; i >= 0 && trackOpen[i] < from; i--)\n                this.openStart++;\n        }\n    }\n    activeForPoint(to) {\n        if (!this.active.length)\n            return this.active;\n        let active = [];\n        for (let i = this.active.length - 1; i >= 0; i--) {\n            if (this.activeRank[i] < this.pointRank)\n                break;\n            if (this.activeTo[i] > to || this.activeTo[i] == to && this.active[i].endSide >= this.point.endSide)\n                active.push(this.active[i]);\n        }\n        return active.reverse();\n    }\n    openEnd(to) {\n        let open = 0;\n        for (let i = this.activeTo.length - 1; i >= 0 && this.activeTo[i] > to; i--)\n            open++;\n        return open;\n    }\n}\nfunction compare(a, startA, b, startB, length, comparator) {\n    a.goto(startA);\n    b.goto(startB);\n    let endB = startB + length;\n    let pos = startB, dPos = startB - startA;\n    for (;;) {\n        let dEnd = (a.to + dPos) - b.to, diff = dEnd || a.endSide - b.endSide;\n        let end = diff < 0 ? a.to + dPos : b.to, clipEnd = Math.min(end, endB);\n        if (a.point || b.point) {\n            if (!(a.point && b.point && (a.point == b.point || a.point.eq(b.point)) &&\n                sameValues(a.activeForPoint(a.to), b.activeForPoint(b.to))))\n                comparator.comparePoint(pos, clipEnd, a.point, b.point);\n        }\n        else {\n            if (clipEnd > pos && !sameValues(a.active, b.active))\n                comparator.compareRange(pos, clipEnd, a.active, b.active);\n        }\n        if (end > endB)\n            break;\n        if ((dEnd || a.openEnd != b.openEnd) && comparator.boundChange)\n            comparator.boundChange(end);\n        pos = end;\n        if (diff <= 0)\n            a.next();\n        if (diff >= 0)\n            b.next();\n    }\n}\nfunction sameValues(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (a[i] != b[i] && !a[i].eq(b[i]))\n            return false;\n    return true;\n}\nfunction remove(array, index) {\n    for (let i = index, e = array.length - 1; i < e; i++)\n        array[i] = array[i + 1];\n    array.pop();\n}\nfunction insert(array, index, value) {\n    for (let i = array.length - 1; i >= index; i--)\n        array[i + 1] = array[i];\n    array[index] = value;\n}\nfunction findMinIndex(value, array) {\n    let found = -1, foundPos = ********** /* C.Far */;\n    for (let i = 0; i < array.length; i++)\n        if ((array[i] - foundPos || value[i].endSide - value[found].endSide) < 0) {\n            found = i;\n            foundPos = array[i];\n        }\n    return found;\n}\n\n/**\nCount the column position at the given offset into the string,\ntaking extending characters and tab size into account.\n*/\nfunction countColumn(string, tabSize, to = string.length) {\n    let n = 0;\n    for (let i = 0; i < to && i < string.length;) {\n        if (string.charCodeAt(i) == 9) {\n            n += tabSize - (n % tabSize);\n            i++;\n        }\n        else {\n            n++;\n            i = findClusterBreak(string, i);\n        }\n    }\n    return n;\n}\n/**\nFind the offset that corresponds to the given column position in a\nstring, taking extending characters and tab size into account. By\ndefault, the string length is returned when it is too short to\nreach the column. Pass `strict` true to make it return -1 in that\nsituation.\n*/\nfunction findColumn(string, col, tabSize, strict) {\n    for (let i = 0, n = 0;;) {\n        if (n >= col)\n            return i;\n        if (i == string.length)\n            break;\n        n += string.charCodeAt(i) == 9 ? tabSize - (n % tabSize) : 1;\n        i = findClusterBreak(string, i);\n    }\n    return strict === true ? -1 : string.length;\n}\n\nexport { Annotation, AnnotationType, ChangeDesc, ChangeSet, CharCategory, Compartment, EditorSelection, EditorState, Facet, Line, MapMode, Prec, Range, RangeSet, RangeSetBuilder, RangeValue, SelectionRange, StateEffect, StateEffectType, StateField, Text, Transaction, codePointAt, codePointSize, combineConfig, countColumn, findClusterBreak, findColumn, fromCodePoint };\n"], "names": [], "sourceRoot": ""}