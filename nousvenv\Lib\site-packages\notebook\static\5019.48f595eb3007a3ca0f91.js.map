{"version": 3, "file": "5019.48f595eb3007a3ca0f91.js?v=48f595eb3007a3ca0f91", "mappings": ";;;;;;;;;;;;;;;AAAO;AACP,kBAAkB;AAClB;;AAEA;AACA,sDAAsD;AACtD,gDAAgD;AAChD,sDAAsD;AACtD,gEAAgE;AAChE,4DAA4D;AAC5D,kFAAkF;AAClF,wDAAwD;AACxD,gEAAgE;AAChE,sDAAsD;AACtD,sDAAsD;AACtD;AACA;AACA;AACA;;AAEA;AACA,4BAA4B,WAAW;;AAEvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM,eAAe;AACrB;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,yBAAyB,OAAO;AAChC;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,kBAAkB;AAClB;AACA,MAAM,mBAAmB;AACzB;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB,kBAAkB;AAClB,kBAAkB,eAAe;AACjC;;AAEA,2CAA2C,IAAI,aAAa,EAAE,aAAa,EAAE;AAC7E;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB,0BAA0B,uBAAuB;AACjD;AACA;;AAEA;AACA,kBAAkB,eAAe;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,eAAe;AACjC,kBAAkB;;AAElB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,eAAe;AACjC;AACA;;AAEA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,0BAA0B,uBAAuB;AACjD,kBAAkB;AAClB;AACA;;AAEA;AACA,kBAAkB;AAClB,kBAAkB,eAAe;AACjC;AACA;AACA;AACA;;AAEA;AACA,kBAAkB;AAClB,kBAAkB,eAAe;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA,wCAAwC;AACxC;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA,UAAU;AACV,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA,4BAA4B;AAC5B,sBAAsB,2BAA2B,yBAAyB;AAC1E;AACA;AACA;AACA;;AAEA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO,kBAAkB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO,mBAAmB,YAAY;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA,8BAA8B;AAC9B;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;AACA;AACA;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,CAAC;;AAEM;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/css.js"], "sourcesContent": ["export function mkCSS(parserConfig) {\n  parserConfig = {...defaults, ...parserConfig}\n  var inline = parserConfig.inline\n\n  var tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = parserConfig.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"def\", \"variable-definition\");\n        return ret(\"variableName\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variableName.function\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : stream.indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override = \"error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override = \"error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variableName.constant\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    name: parserConfig.name,\n    startState: function() {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n                   ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - iCx.unit);\n        }\n      }\n      return indent;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*\\}$/,\n      commentTokens: {line: lineComment, block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: allWords\n    }\n  };\n};\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i].toLowerCase()] = true;\n  }\n  return keys;\n}\n\nvar documentTypes_ = [\n  \"domain\", \"regexp\", \"url\", \"url-prefix\"\n], documentTypes = keySet(documentTypes_);\n\nvar mediaTypes_ = [\n  \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n  \"tty\", \"tv\", \"embossed\"\n], mediaTypes = keySet(mediaTypes_);\n\nvar mediaFeatures_ = [\n  \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n  \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n  \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n  \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n  \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n  \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n  \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n  \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n  \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n  \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n  \"dynamic-range\", \"video-dynamic-range\"\n], mediaFeatures = keySet(mediaFeatures_);\n\nvar mediaValueKeywords_ = [\n  \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n  \"interlace\", \"progressive\",\n  \"dark\", \"light\",\n  \"standard\", \"high\"\n], mediaValueKeywords = keySet(mediaValueKeywords_);\n\nvar propertyKeywords_ = [\n  \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n  \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n  \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n  \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n  \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n  \"backface-visibility\", \"background\", \"background-attachment\",\n  \"background-blend-mode\", \"background-clip\", \"background-color\",\n  \"background-image\", \"background-origin\", \"background-position\",\n  \"background-position-x\", \"background-position-y\", \"background-repeat\",\n  \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n  \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n  \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n  \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n  \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n  \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n  \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n  \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n  \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n  \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n  \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n  \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n  \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n  \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n  \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n  \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n  \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n  \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n  \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n  \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n  \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n  \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n  \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n  \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n  \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n  \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n  \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n  \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n  \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n  \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n  \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n  \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n  \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n  \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n  \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n  \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n  \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n  \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n  \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n  \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n  \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n  \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n  \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n  \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n  \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n  \"max-block-size\", \"max-height\", \"max-inline-size\",\n  \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n  \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n  \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n  \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n  \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n  \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n  \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n  \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n  \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n  \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n  \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n  \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n  \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n  \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n  \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n  \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n  \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n  \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n  \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n  \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n  \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n  \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n  \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n  \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n  \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n  \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n  \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n  \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n  \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n  \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n  \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n  \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n  \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n  \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n  \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n  \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n  \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n  \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n  \"transition-property\", \"transition-timing-function\", \"translate\",\n  \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n  \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n  \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n  \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n  // SVG-specific\n  \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n  \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n  \"color-interpolation\", \"color-interpolation-filters\",\n  \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n  \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n  \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n  \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n  \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n  \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n], propertyKeywords = keySet(propertyKeywords_);\n\nvar nonStandardPropertyKeywords_ = [\n  \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n  \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n  \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n  \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n  \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n  \"border-inline-end-color\", \"border-inline-end-style\",\n  \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n  \"border-inline-start-style\", \"border-inline-start-width\",\n  \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n  \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n  \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n  \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n  \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n  \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n  \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n  \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\nvar fontProperties_ = [\n  \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n  \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n], fontProperties = keySet(fontProperties_);\n\nvar counterDescriptors_ = [\n  \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n  \"speak-as\", \"suffix\", \"symbols\", \"system\"\n], counterDescriptors = keySet(counterDescriptors_);\n\nvar colorKeywords_ = [\n  \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n  \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n  \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n  \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n  \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n  \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n  \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n  \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n  \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n  \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n  \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n  \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n  \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n  \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n  \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n  \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n  \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n  \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n  \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n  \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n  \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n  \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n  \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n  \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n  \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n  \"whitesmoke\", \"yellow\", \"yellowgreen\"\n], colorKeywords = keySet(colorKeywords_);\n\nvar valueKeywords_ = [\n  \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n  \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n  \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n  \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n  \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n  \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n  \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n  \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n  \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n  \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n  \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n  \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n  \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n  \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n  \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n  \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n  \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n  \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n  \"dot-dash\", \"dot-dot-dash\",\n  \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n  \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n  \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n  \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n  \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n  \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n  \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n  \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n  \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n  \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n  \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n  \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n  \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n  \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n  \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n  \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n  \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n  \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n  \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n  \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n  \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n  \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n  \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n  \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n  \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n  \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n  \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n  \"menu\", \"menulist\", \"menulist-button\",\n  \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n  \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n  \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n  \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n  \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n  \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n  \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n  \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n  \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n  \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n  \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n  \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n  \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n  \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n  \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n  \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n  \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n  \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n  \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n  \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n  \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n  \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n  \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n  \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n  \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n  \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n  \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n  \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n  \"tamil\",\n  \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n  \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n  \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n  \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n  \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n  \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n  \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n  \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n  \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n  \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n  \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n  \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n  \"xx-large\", \"xx-small\"\n], valueKeywords = keySet(valueKeywords_);\n\nvar allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n\nexport const keywords = {properties: propertyKeywords_, colors: colorKeywords_,\n                         fonts: fontProperties_, values: valueKeywords_, all: allWords}\n\nconst defaults = {\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n}\n\nexport const css = mkCSS({name: \"css\"})\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\nexport const sCSS = mkCSS({\n  name: \"scss\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \":\": function(stream) {\n      if (stream.match(/^\\s*\\{/, false))\n        return [null, null]\n      return false;\n    },\n    \"$\": function(stream) {\n      stream.match(/^[\\w-]+/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName.special\", \"variable\"];\n    },\n    \"#\": function(stream) {\n      if (!stream.eat(\"{\")) return false;\n      return [null, \"interpolation\"];\n    }\n  }\n})\n\nexport const less = mkCSS({\n  name: \"less\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \"@\": function(stream) {\n      if (stream.eat(\"{\")) return [null, \"interpolation\"];\n      if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName\", \"variable\"];\n    },\n    \"&\": function() {\n      return [\"atom\", \"atom\"];\n    }\n  }\n})\n\nexport const gss = mkCSS({\n  name: \"gss\",\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  supportsAtComponent: true,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n})\n"], "names": [], "sourceRoot": ""}