# Data files
*.csv
*.tsv
*.xlsx
*.xls
*.json
data/
datasets/
raw_data/
processed_data/

# Virtual environments
venv/
nousvenv/
env/
ENV/
.venv/
.ENV/
virtualenv/
__pycache__/

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# Jupyter NBConvert
*.nbconvert.ipynb

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.orig

# Output files from data processing
*.pkl
*.pickle
*.h5
*.hdf5
model_*.pkl
*.model
results/
outputs/
logs/

# Analysis outputs
*.png
*.jpg
*.jpeg
*.gif
*.svg
plots/
figures/
visualizations/