# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Set up visualization style
plt.style.use('default')
sns.set_palette('husl')

# Configure pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

print("Libraries imported successfully!")

{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Data Analysis and Feature Engineering\n",
    "\n",
    "This notebook implements a comprehensive data analysis and feature engineering for the challenge\n",
    ".\n",
    "\n",
    "## Table of Contents\n",
    "1. [Setup and Data Loading](#Setup-and-Data-Loading)\n",
    "2. [Exploratory Data Analysis (EDA)](#Exploratory-Data-Analysis-EDA)\n",
    "3. [Feature Engineering](#Feature-Engineering)\n",
    "4. [Data Preparation for Modeling](#Data-Preparation-for-Modeling)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 1: Setup and Data Loading\n",
    "\n",
    "Import necessary libraries and load the challenge data."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Libraries imported successfully!\n"
     ]
    }
   ],
   "source": [
    "# Import required libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "\n",
    "# Set up visualization style\n",
    "plt.style.use('default')\n",
    "sns.set_palette('husl')\n",
    "\n",
    "# Configure pandas display options\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.width', None)\n",
    "pd.set_option('display.max_colwidth', None)\n",
    "\n",
    "print(\"Libraries imported successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "metadata": {},
   "outputs": [
    {
     "ename": "ParserError",
     "evalue": "Error tokenizing data. C error: Expected 14 fields in line 9, saw 30\n",
     "output_type": "error",
     "traceback": [
      "\u001b[31m---------------------------------------------------------------------------\u001b[39m",
      "\u001b[31mParserError\u001b[39m                               Traceback (most recent call last)",
      "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Load the CSV file into a pandas DataFrame\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m df = \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mchallenge_data-18-ago.csv\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mData loaded successfully! Shape: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdf.shape\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mColumns: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlist\u001b[39m(df.columns)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n",
      "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\GitHub\\NousGraph\\nousvenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[39m, in \u001b[36mread_csv\u001b[39m\u001b[34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[39m\n\u001b[32m   1013\u001b[39m kwds_defaults = _refine_defaults_read(\n\u001b[32m   1014\u001b[39m     dialect,\n\u001b[32m   1015\u001b[39m     delimiter,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1022\u001b[39m     dtype_backend=dtype_backend,\n\u001b[32m   1023\u001b[39m )\n\u001b[32m   1024\u001b[39m kwds.update(kwds_defaults)\n\u001b[32m-> \u001b[39m\u001b[32m1026\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\GitHub\\NousGraph\\nousvenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:626\u001b[39m, in \u001b[36m_read\u001b[39m\u001b[34m(filepath_or_buffer, kwds)\u001b[39m\n\u001b[32m    623\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n\u001b[32m    625\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m parser:\n\u001b[32m--> \u001b[39m\u001b[32m626\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mparser\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\GitHub\\NousGraph\\nousvenv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1923\u001b[39m, in \u001b[36mTextFileReader.read\u001b[39m\u001b[34m(self, nrows)\u001b[39m\n\u001b[32m   1916\u001b[39m nrows = validate_integer(\u001b[33m\"\u001b[39m\u001b[33mnrows\u001b[39m\u001b[33m\"\u001b[39m, nrows)\n\u001b[32m   1917\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m   1918\u001b[39m     \u001b[38;5;66;03m# error: \"ParserBase\" has no attribute \"read\"\u001b[39;00m\n\u001b[32m   1919\u001b[39m     (\n\u001b[32m   1920\u001b[39m         index,\n\u001b[32m   1921\u001b[39m         columns,\n\u001b[32m   1922\u001b[39m         col_dict,\n\u001b[32m-> \u001b[39m\u001b[32m1923\u001b[39m     ) = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_engine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[attr-defined]\u001b[39;49;00m\n\u001b[32m   1924\u001b[39m \u001b[43m        \u001b[49m\u001b[43mnrows\u001b[49m\n\u001b[32m   1925\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1926\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[32m   1927\u001b[39m     \u001b[38;5;28mself\u001b[39m.close()\n",
      "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\GitHub\\NousGraph\\nousvenv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py:234\u001b[39m, in \u001b[36mCParserWrapper.read\u001b[39m\u001b[34m(self, nrows)\u001b[39m\n\u001b[32m    232\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    233\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.low_memory:\n\u001b[32m--> \u001b[39m\u001b[32m234\u001b[39m         chunks = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_reader\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_low_memory\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnrows\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    235\u001b[39m         \u001b[38;5;66;03m# destructive to chunks\u001b[39;00m\n\u001b[32m    236\u001b[39m         data = _concatenate_chunks(chunks)\n",
      "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/parsers.pyx:838\u001b[39m, in \u001b[36mpandas._libs.parsers.TextReader.read_low_memory\u001b[39m\u001b[34m()\u001b[39m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/parsers.pyx:905\u001b[39m, in \u001b[36mpandas._libs.parsers.TextReader._read_rows\u001b[39m\u001b[34m()\u001b[39m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/parsers.pyx:874\u001b[39m, in \u001b[36mpandas._libs.parsers.TextReader._tokenize_rows\u001b[39m\u001b[34m()\u001b[39m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/parsers.pyx:891\u001b[39m, in \u001b[36mpandas._libs.parsers.TextReader._check_tokenize_status\u001b[39m\u001b[34m()\u001b[39m\n",
      "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/parsers.pyx:2061\u001b[39m, in \u001b[36mpandas._libs.parsers.raise_parser_error\u001b[39m\u001b[34m()\u001b[39m\n",
      "\u001b[31mParserError\u001b[39m: Error tokenizing data. C error: Expected 14 fields in line 9, saw 30\n"
     ]
    }
   ],
   "source": [
    "# Load the CSV file into a pandas DataFrame\n",
    "df = pd.read_csv('challenge_data-18-ago.csv')\n",
    "\n",
    "print(f\"Data loaded successfully! Shape: {df.shape}\")\n",
    "print(f\"Columns: {list(df.columns)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 2: Exploratory Data Analysis (EDA)\n",
    "\n",
    "Perform initial data inspection to understand the dataset structure and characteristics."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# View the first few rows\n",
    "print(\"First 5 rows of the dataset:\")\n",
    "df.head()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Get DataFrame information\n",
    "print(\"\\nDataFrame Information:\")\n",
    "df.info()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Get descriptive statistics\n",
    "print(\"\\nDescriptive Statistics:\")\n",
    "df.describe()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check for missing values\n",
    "print(\"\\nMissing Values Count:\")\n",
    "missing_values = df.isnull().sum()\n",
    "print(missing_values)\n",
    "\n",
    "print(f\"\\nTotal missing values: {missing_values.sum()}\")\n",
    "print(f\"Percentage of missing data: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### Data Visualization\n",
    "\n",
    "Create various visualizations to understand data distributions and relationships."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Histograms for numerical features\n",
    "numerical_cols = df.select_dtypes(include=[np.number]).columns\n",
    "\n",
    "if len(numerical_cols) > 0:\n",
    "    print(f\"Creating histograms for {len(numerical_cols)} numerical columns...\")\n",
    "    \n",
    "    # Calculate grid dimensions\n",
    "    n_cols = 3\n",
    "    n_rows = (len(numerical_cols) + n_cols - 1) // n_cols\n",
    "    \n",
    "    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n",
    "    if n_rows == 1:\n",
    "        axes = axes.reshape(1, -1)\n",
    "    \n",
    "    for i, col in enumerate(numerical_cols):\n",
    "        row_idx = i // n_cols\n",
    "        col_idx = i % n_cols\n",
    "        \n",
    "        if n_rows > 1:\n",
    "            ax = axes[row_idx, col_idx]\n",
    "        else:\n",
    "            ax = axes[col_idx]\n",
    "        \n",
    "        df[col].hist(bins=30, ax=ax)\n",
    "        ax.set_title(f'Distribution of {col}')\n",
    "        ax.set_xlabel(col)\n",
    "        ax.set_ylabel('Frequency')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "else:\n",
    "    print(\"No numerical columns found for histograms.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Box plots to identify outliers\n",
    "if len(numerical_cols) > 0:\n",
    "    print(\"Creating box plots to identify outliers...\")\n",
    "    \n",
    "    plt.figure(figsize=(15, 8))\n",
    "    sns.boxplot(data=df[numerical_cols])\n",
    "    plt.title('Box Plots for Numerical Features')\n",
    "    plt.xticks(rotation=45)\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "else:\n",
    "    print(\"No numerical columns found for box plots.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Correlation matrix heatmap\n",
    "if len(numerical_cols) > 1:\n",
    "    print(\"Creating correlation matrix heatmap...\")\n",
    "    \n",
    "    plt.figure(figsize=(12, 8))\n",
    "    correlation_matrix = df[numerical_cols].corr()\n",
    "    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', \n",
    "                linewidths=0.5, cbar_kws={'shrink': 0.8})\n",
    "    plt.title('Correlation Matrix Heatmap')\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    # Print highly correlated pairs\n",
    "    print(\"\\nHighly correlated pairs (|correlation| > 0.7):\")\n",
    "    high_corr_pairs = []\n",
    "    for i in range(len(correlation_matrix.columns)):\n",
    "        for j in range(i+1, len(correlation_matrix.columns)):\n",
    "            if abs(correlation_matrix.iloc[i, j]) > 0.7:\n",
    "                high_corr_pairs.append({\n",
    "                    'Variable 1': correlation_matrix.columns[i],\n",
    "                    'Variable 2': correlation_matrix.columns[j],\n",
    "                    'Correlation': correlation_matrix.iloc[i, j]\n",
    "                })\n",
    "    \n",
    "    if high_corr_pairs:\n",
    "        high_corr_df = pd.DataFrame(high_corr_pairs)\n",
    "        print(high_corr_df)\n",
    "    else:\n",
    "        print(\"No highly correlated pairs found.\")\n",
    "else:\n",
    "    print(\"Insufficient numerical columns for correlation analysis.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Pair plots for smaller datasets\n",
    "# Note: Pair plots can be computationally expensive for large datasets\n",
    "max_pairplot_cols = 5  # Limit to prevent performance issues\n",
    "\n",
    "if len(numerical_cols) <= max_pairplot_cols and len(numerical_cols) > 1:\n",
    "    print(f\"Creating pair plots for {len(numerical_cols)} numerical variables...\")\n",
    "    sns.pairplot(df[numerical_cols], diag_kind='kde')\n",
    "    plt.suptitle('Pair Plot of Numerical Variables', y=1.02)\n",
    "    plt.show()\n",
    "elif len(numerical_cols) > max_pairplot_cols:\n",
    "    print(f\"Dataset has {len(numerical_cols)} numerical columns. Pair plot skipped to avoid performance issues.\")\n",
    "    print(f\"Consider sampling or selecting specific columns for pair plot analysis.\")\n",
    "else:\n",
    "    print(\"Insufficient numerical columns for pair plots.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 3: Feature Engineering\n",
    "\n",
    "Based on the EDA results, perform feature engineering operations."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a copy of the dataframe for feature engineering\n",
    "df_engineered = df.copy()\n",
    "\n",
    "print(\"Starting feature engineering process...\")\n",
    "print(f\"Original dataset shape: {df_engineered.shape}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Handle Missing Values\n",
    "print(\"\\nHandling missing values...\")\n",
    "\n",
    "# Get columns with missing values\n",
    "missing_cols = df_engineered.columns[df_engineered.isnull().any()]\n",
    "\n",
    "if len(missing_cols) > 0:\n",
    "    for col in missing_cols:\n",
    "        missing_count = df_engineered[col].isnull().sum()\n",
    "        missing_pct = (missing_count / len(df_engineered)) * 100\n",
    "        \n",
    "        print(f\"\\nColumn '{col}': {missing_count} missing values ({missing_pct:.2f}%)\")\n",
    "        \n",
    "        if df_engineered[col].dtype in ['int64', 'float64']:\n",
    "            # Numerical column - use median\n",
    "            median_val = df_engineered[col].median()\n",
    "            df_engineered[col].fillna(median_val, inplace=True)\n",
    "            print(f\"  -> Filled with median: {median_val}\")\n",
    "        else:\n",
    "            # Categorical column - use mode\n",
    "            mode_val = df_engineered[col].mode()[0]\n",
    "            df_engineered[col].fillna(mode_val, inplace=True)\n",
    "            print(f\"  -> Filled with mode: {mode_val}\")\n",
    "else:\n",
    "    print(\"No missing values found in the dataset.\")\n",
    "\n",
    "print(f\"\\nAfter handling missing values, shape: {df_engineered.shape}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Encode Categorical Variables\n",
    "print(\"\\nEncoding categorical variables...\")\n",
    "\n",
    "categorical_cols = df_engineered.select_dtypes(include=['object', 'category']).columns\n",
    "print(f\"Found {len(categorical_cols)} categorical columns: {list(categorical_cols)}\")\n",
    "\n",
    "if len(categorical_cols) > 0:\n",
    "    from sklearn.preprocessing import LabelEncoder\n",
    "    \n",
    "    # Apply label encoding to categorical columns\n",
    "    label_encoders = {}\n",
    "    \n",
    "    for col in categorical_cols:\n",
    "        unique_values = df_engineered[col].nunique()\n",
    "        print(f\"\\nColumn '{col}': {unique_values} unique values\")\n",
    "        \n",
    "        # For columns with 2 unique values, use label encoding\n",
    "        if unique_values == 2:\n",
    "            le = LabelEncoder()\n",
    "            df_engineered[col] = le.fit_transform(df_engineered[col])\n",
    "            label_encoders[col] = le\n",
    "            print(f\"  -> Applied label encoding\")\n",
    "        \n",
    "        # For columns with more than 2 unique values, use one-hot encoding\n",
    "        elif unique_values <= 10:  # Reasonable limit for one-hot encoding\n",
    "            # Create dummy variables\n",
    "            dummies = pd.get_dummies(df_engineered[col], prefix=col, drop_first=True)\n",
    "            df_engineered = pd.concat([df_engineered, dummies], axis=1)\n",
    "            df_engineered = df_engineered.drop(col, axis=1)\n",
    "            print(f\"  -> Applied one-hot encoding, created {dummies.shape[1]} new columns\")\n",
    "        \n",
    "        else:\n",
    "            print(f\"  -> Column has too many unique values ({unique_values}), consider grouping or alternative encoding\")\n",
    "    \n",
    "    print(f\"\\nAfter categorical encoding, shape: {df_engineered.shape}\")\n",
    "else:\n",
    "    print(\"No categorical columns found.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create New Features (Domain-specific)\n",
    "print(\"\\nCreating new features...\")\n",
    "\n",
    "# Get numerical columns for feature creation\n",
    "numerical_cols = df_engineered.select_dtypes(include=[np.number]).columns\n",
    "print(f\"Available numerical columns for feature engineering: {list(numerical_cols)}\")\n",
    "\n",
    "new_features_created = 0\n",
    "\n",
    "# Example feature engineering based on common patterns\n",
    "if len(numerical_cols) >= 2:\n",
    "    # Create interaction features (ratios, products, differences)\n",
    "    for i, col1 in enumerate(numerical_cols):\n",
    "        for col2 in numerical_cols[i+1:]:\n",
    "            # Avoid division by zero\n",
    "            if df_engineered[col2].min() > 0:\n",
    "                # Ratio feature\n",
    "                ratio_name = f\"{col1}_div_{col2}\"\n",
    "                df_engineered[ratio_name] = df_engineered[col1] / df_engineered[col2]\n",
    "                new_features_created += 1\n",
    "                \n",
    "                # Product feature\n",
    "                product_name = f\"{col1}_times_{col2}\"\n",
    "                df_engineered[product_name] = df_engineered[col1] * df_engineered[col2]\n",
    "                new_features_created += 1\n",
    "                \n",
    "                # Difference feature\n",
    "                diff_name = f\"{col1}_minus_{col2}\"\n",
    "                df_engineered[diff_name] = df_engineered[col1] - df_engineered[col2]\n",
    "                new_features_created += 1\n",
    "    \n",
    "    print(f\"Created {new_features_created} new interaction features\")\n",
    "else:\n",
    "    print(\"Insufficient numerical columns for interaction feature creation.\")\n",
    "\n",
    "# Create statistical features if we have enough columns\n",
    "if len(numerical_cols) >= 3:\n",
    "    # Row-wise statistics\n",
    "    df_engineered['row_mean'] = df_engineered[numerical_cols].mean(axis=1)\n",
    "    df_engineered['row_std'] = df_engineered[numerical_cols].std(axis=1)\n",
    "    df_engineered['row_min'] = df_engineered[numerical_cols].min(axis=1)\n",
    "    df_engineered['row_max'] = df_engineered[numerical_cols].max(axis=1)\n",
    "    new_features_created += 4\n",
    "    print(\"Created row-wise statistical features (mean, std, min, max)\")\n",
    "\n",
    "print(f\"\\nFeature engineering completed. Final shape: {df_engineered.shape}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature Scaling\n",
    "print(\"\\nApplying feature scaling...\")\n",
    "\n",
    "# Separate numerical columns for scaling\n",
    "numerical_cols_final = df_engineered.select_dtypes(include=[np.number]).columns\n",
    "print(f\"Scaling {len(numerical_cols_final)} numerical columns\")\n",
    "\n",
    "if len(numerical_cols_final) > 0:\n",
    "    from sklearn.preprocessing import StandardScaler, MinMaxScaler\n",
    "    \n",
    "    # Create copies for different scaling methods\n",
    "    df_standardized = df_engineered.copy()\n",
    "    df_normalized = df_engineered.copy()\n",
    "    \n",
    "    # Standardization (Z-score normalization)\n",
    "    print(\"Applying standardization (Z-score normalization)...\")\n",
    "    scaler_standard = StandardScaler()\n",
    "    df_standardized[numerical_cols_final] = scaler_standard.fit_transform(df_standardized[numerical_cols_final])\n",
    "    \n",
    "    # Normalization (Min-Max scaling)\n",
    "    print(\"Applying normalization (Min-Max scaling)...\")\n",
    "    scaler_minmax = MinMaxScaler()\n",
    "    df_normalized[numerical_cols_final] = scaler_minmax.fit_transform(df_normalized[numerical_cols_final])\n",
    "    \n",
    "    print(\"\\nScaling completed successfully!\")\n",
    "    print(f\"Standardized data shape: {df_standardized.shape}\")\n",
    "    print(f\"Normalized data shape: {df_normalized.shape}\")\n",
    "else:\n",
    "    print(\"No numerical columns found for scaling.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 4: Data Preparation for Modeling\n",
    "\n",
    "Prepare the engineered datasets for machine learning modeling."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Summary of all datasets\n",
    "print(\"=== DATA ANALYSIS AND FEATURE ENGINEERING SUMMARY ===\\n\")\n",
    "\n",
    "print(f\"Original dataset shape: {df.shape}\")\n",
    "print(f\"Engineered dataset shape: {df_engineered.shape}\")\n",
    "if 'df_standardized' in locals():\n",
    "    print(f\"Standardized dataset shape: {df_standardized.shape}\")\n",
    "if 'df_normalized' in locals():\n",
    "    print(f\"Normalized dataset shape: {df_normalized.shape}\")\n",
    "\n",
    "print(f\"\\nNew features created: {new_features_created}\")\n",
    "\n",
    "# Display final column types\n",
    "print(\"\\nFinal dataset column types:\")\n",
    "column_types = df_engineered.dtypes.value_counts()\n",
    "for dtype, count in column_types.items():\n",
    "    print(f\"  {dtype}: {count} columns\")\n",
    "\n",
    "# Check for any remaining issues\n",
    "remaining_missing = df_engineered.isnull().sum().sum()\n",
    "if remaining_missing == 0:\n",
    "    print(\"\\n✅ No missing values remain in the dataset\")\n",
    "else:\n",
    "    print(f\"\\n⚠️  {remaining_missing} missing values still remain\")\n",
    "\n",
    "print(\"\\n=== DATASETS READY FOR MODELING ===\")\n",
    "print(\"Available datasets:\")\n",
    "print(\"1. df_engineered - Original engineered dataset\")\n",
    "print(\"2. df_standardized - Standardized (Z-score normalized) dataset\")\n",
    "print(\"3. df_normalized - Normalized (Min-Max scaled) dataset\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Export processed datasets (optional)\n",
    "print(\"\\nExporting processed datasets...\")\n",
    "\n",
    "try:\n",
    "    df_engineered.to_csv('engineered_data.csv', index=False)\n",
    "    print(\"✅ Engineered dataset exported to 'engineered_data.csv'\")\n",
    "    \n",
    "    if 'df_standardized' in locals():\n",
    "        df_standardized.to_csv('standardized_data.csv', index=False)\n",
    "        print(\"✅ Standardized dataset exported to 'standardized_data.csv'\")\n",
    "    \n",
    "    if 'df_normalized' in locals():\n",
    "        df_normalized.to_csv('normalized_data.csv', index=False)\n",
    "        print(\"✅ Normalized dataset exported to 'normalized_data.csv'\")\n",
    "        \n",
    "    print(\"\\nAll datasets have been successfully exported!\")\n",
    "except Exception as e:\n",
    "    print(f\"\\n⚠️  Error exporting datasets: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final recommendations for modeling\n",
    "print(\"\\n=== MODELING RECOMMENDATIONS ===\")\n",
    "print(\"Based on the analysis, here are recommendations for next steps:\")\n",
    "print(\"\\n1. Choose appropriate dataset based on your ML algorithm:\")\n",
    "print(\"   - Use standardized_data.csv for algorithms sensitive to feature scales (SVM, KNN, PCA)\")\n",
    "print(\"   - Use normalized_data.csv for neural networks and algorithms requiring bounded inputs\")\n",
    "print(\"   - Use engineered_data.csv for tree-based algorithms (Random Forest, XGBoost)\")\n",
    "\n",
    "print(\"\\n2. Consider feature selection techniques:\")\n",
    "print(\"   - Check correlation matrix for multicollinearity\")\n",
    "print(\"   - Use feature importance from tree-based models\")\n",
    "print(\"   - Apply dimensionality reduction (PCA) if needed\")\n",
    "\n",
    "print(\"\\n3. Next modeling steps:\")\n",
    "print(\"   - Split data into training and testing sets\")\n",
    "print(\"   - Train baseline models\")\n",
    "print(\"   - Evaluate and tune model performance\")\n",
    "print(\"   - Consider ensemble methods for improved accuracy\")\n",
    "\n",
    "print(\"\\n🎉 Data analysis and feature engineering pipeline completed successfully!\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "nousvenv",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.8"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}


# View the first few rows
print("First 5 rows of the dataset:")
df.head()

# Get DataFrame information
print("\nDataFrame Information:")
df.info()

# Get descriptive statistics
print("\nDescriptive Statistics:")
df.describe()

# Check for missing values
print("\nMissing Values Count:")
missing_values = df.isnull().sum()
print(missing_values)

print(f"\nTotal missing values: {missing_values.sum()}")
print(f"Percentage of missing data: {(missing_values.sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%")

# Histograms for numerical features
numerical_cols = df.select_dtypes(include=[np.number]).columns

if len(numerical_cols) > 0:
    print(f"Creating histograms for {len(numerical_cols)} numerical columns...")
    
    # Calculate grid dimensions
    n_cols = 3
    n_rows = (len(numerical_cols) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, col in enumerate(numerical_cols):
        row_idx = i // n_cols
        col_idx = i % n_cols
        
        if n_rows > 1:
            ax = axes[row_idx, col_idx]
        else:
            ax = axes[col_idx]
        
        df[col].hist(bins=30, ax=ax)
        ax.set_title(f'Distribution of {col}')
        ax.set_xlabel(col)
        ax.set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.show()
else:
    print("No numerical columns found for histograms.")

# Box plots to identify outliers
if len(numerical_cols) > 0:
    print("Creating box plots to identify outliers...")
    
    plt.figure(figsize=(15, 8))
    sns.boxplot(data=df[numerical_cols])
    plt.title('Box Plots for Numerical Features')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
else:
    print("No numerical columns found for box plots.")

# Correlation matrix heatmap
if len(numerical_cols) > 1:
    print("Creating correlation matrix heatmap...")
    
    plt.figure(figsize=(12, 8))
    correlation_matrix = df[numerical_cols].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', 
                linewidths=0.5, cbar_kws={'shrink': 0.8})
    plt.title('Correlation Matrix Heatmap')
    plt.tight_layout()
    plt.show()
    
    # Print highly correlated pairs
    print("\nHighly correlated pairs (|correlation| > 0.7):")
    high_corr_pairs = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            if abs(correlation_matrix.iloc[i, j]) > 0.7:
                high_corr_pairs.append({
                    'Variable 1': correlation_matrix.columns[i],
                    'Variable 2': correlation_matrix.columns[j],
                    'Correlation': correlation_matrix.iloc[i, j]
                })
    
    if high_corr_pairs:
        high_corr_df = pd.DataFrame(high_corr_pairs)
        print(high_corr_df)
    else:
        print("No highly correlated pairs found.")
else:
    print("Insufficient numerical columns for correlation analysis.")

# Pair plots for smaller datasets
# Note: Pair plots can be computationally expensive for large datasets
max_pairplot_cols = 5  # Limit to prevent performance issues

if len(numerical_cols) <= max_pairplot_cols and len(numerical_cols) > 1:
    print(f"Creating pair plots for {len(numerical_cols)} numerical variables...")
    sns.pairplot(df[numerical_cols], diag_kind='kde')
    plt.suptitle('Pair Plot of Numerical Variables', y=1.02)
    plt.show()
elif len(numerical_cols) > max_pairplot_cols:
    print(f"Dataset has {len(numerical_cols)} numerical columns. Pair plot skipped to avoid performance issues.")
    print(f"Consider sampling or selecting specific columns for pair plot analysis.")
else:
    print("Insufficient numerical columns for pair plots.")

# Create a copy of the dataframe for feature engineering
df_engineered = df.copy()

print("Starting feature engineering process...")
print(f"Original dataset shape: {df_engineered.shape}")

# Handle Missing Values
print("\nHandling missing values...")

# Get columns with missing values
missing_cols = df_engineered.columns[df_engineered.isnull().any()]

if len(missing_cols) > 0:
    for col in missing_cols:
        missing_count = df_engineered[col].isnull().sum()
        missing_pct = (missing_count / len(df_engineered)) * 100
        
        print(f"\nColumn '{col}': {missing_count} missing values ({missing_pct:.2f}%)")
        
        if df_engineered[col].dtype in ['int64', 'float64']:
            # Numerical column - use median
            median_val = df_engineered[col].median()
            df_engineered[col].fillna(median_val, inplace=True)
            print(f"  -> Filled with median: {median_val}")
        else:
            # Categorical column - use mode
            mode_val = df_engineered[col].mode()[0]
            df_engineered[col].fillna(mode_val, inplace=True)
            print(f"  -> Filled with mode: {mode_val}")
else:
    print("No missing values found in the dataset.")

print(f"\nAfter handling missing values, shape: {df_engineered.shape}")

# Encode Categorical Variables
print("\nEncoding categorical variables...")

categorical_cols = df_engineered.select_dtypes(include=['object', 'category']).columns
print(f"Found {len(categorical_cols)} categorical columns: {list(categorical_cols)}")

if len(categorical_cols) > 0:
    from sklearn.preprocessing import LabelEncoder
    
    # Apply label encoding to categorical columns
    label_encoders = {}
    
    for col in categorical_cols:
        unique_values = df_engineered[col].nunique()
        print(f"\nColumn '{col}': {unique_values} unique values")
        
        # For columns with 2 unique values, use label encoding
        if unique_values == 2:
            le = LabelEncoder()
            df_engineered[col] = le.fit_transform(df_engineered[col])
            label_encoders[col] = le
            print(f"  -> Applied label encoding")
        
        # For columns with more than 2 unique values, use one-hot encoding
        elif unique_values <= 10:  # Reasonable limit for one-hot encoding
            # Create dummy variables
            dummies = pd.get_dummies(df_engineered[col], prefix=col, drop_first=True)
            df_engineered = pd.concat([df_engineered, dummies], axis=1)
            df_engineered.drop(col, axis=1, inplace=True)
            print(f"  -> Applied one-hot encoding, created {dummies.shape[1]} new columns")
        
        else:
            print(f"  -> Column has too many unique values ({unique_values}), consider grouping or alternative encoding")
    
    print(f"\nAfter categorical encoding, shape: {df_engineered.shape}")
else:
    print("No categorical columns found.")

# Create New Features (Domain-specific)
print("\nCreating new features...")

# Get numerical columns for feature creation
numerical_cols = df_engineered.select_dtypes(include=[np.number]).columns
print(f"Available numerical columns for feature engineering: {list(numerical_cols)}")

new_features_created = 0

# Example feature engineering based on common patterns
if len(numerical_cols) >= 2:
    # Create interaction features (ratios, products, differences)
    for i, col1 in enumerate(numerical_cols):
        for col2 in numerical_cols[i+1:]:
            # Avoid division by zero
            if df_engineered[col2].min() > 0:
                # Ratio feature
                ratio_name = f"{col1}_div_{col2}"
                df_engineered[ratio_name] = df_engineered[col1] / df_engineered[col2]
                new_features_created += 1
                
                # Product feature
                product_name = f"{col1}_times_{col2}"
                df_engineered[product_name] = df_engineered[col1] * df_engineered[col2]
                new_features_created += 1
                
                # Difference feature
                diff_name = f"{col1}_minus_{col2}"
                df_engineered[diff_name] = df_engineered[col1] - df_engineered[col2]
                new_features_created += 1
    
    print(f"Created {new_features_created} new interaction features")
else:
    print("Insufficient numerical columns for interaction feature creation.")

# Create statistical features if we have enough columns
if len(numerical_cols) >= 3:
    # Row-wise statistics
    df_engineered['row_mean'] = df_engineered[numerical_cols].mean(axis=1)
    df_engineered['row_std'] = df_engineered[numerical_cols].std(axis=1)
    df_engineered['row_min'] = df_engineered[numerical_cols].min(axis=1)
    df_engineered['row_max'] = df_engineered[numerical_cols].max(axis=1)
    new_features_created += 4
    print("Created row-wise statistical features (mean, std, min, max)")

print(f"\nFeature engineering completed. Final shape: {df_engineered.shape}")

# Feature Scaling
print("\nApplying feature scaling...")

# Separate numerical columns for scaling
numerical_cols_final = df_engineered.select_dtypes(include=[np.number]).columns
print(f"Scaling {len(numerical_cols_final)} numerical columns")

if len(numerical_cols_final) > 0:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    
    # Create copies for different scaling methods
    df_standardized = df_engineered.copy()
    df_normalized = df_engineered.copy()
    
    # Standardization (Z-score normalization)
    print("Applying standardization (Z-score normalization)...")
    scaler_standard = StandardScaler()
    df_standardized[numerical_cols_final] = scaler_standard.fit_transform(df_standardized[numerical_cols_final])
    
    # Normalization (Min-Max scaling)
    print("Applying normalization (Min-Max scaling)...")
    scaler_minmax = MinMaxScaler()
    df_normalized[numerical_cols_final] = scaler_minmax.fit_transform(df_normalized[numerical_cols_final])
    
    print("\nScaling completed successfully!")
    print(f"Standardized data shape: {df_standardized.shape}")
    print(f"Normalized data shape: {df_normalized.shape}")
else:
    print("No numerical columns found for scaling.")

# Summary of all datasets
print("=== DATA ANALYSIS AND FEATURE ENGINEERING SUMMARY ===\n")

print(f"Original dataset shape: {df.shape}")
print(f"Engineered dataset shape: {df_engineered.shape}")
if 'df_standardized' in locals():
    print(f"Standardized dataset shape: {df_standardized.shape}")
if 'df_normalized' in locals():
    print(f"Normalized dataset shape: {df_normalized.shape}")

print(f"\nNew features created: {new_features_created}")

# Display final column types
print("\nFinal dataset column types:")
column_types = df_engineered.dtypes.value_counts()
for dtype, count in column_types.items():
    print(f"  {dtype}: {count} columns")

# Check for any remaining issues
remaining_missing = df_engineered.isnull().sum().sum()
if remaining_missing == 0:
    print("\n✅ No missing values remain in the dataset")
else:
    print(f"\n⚠️  {remaining_missing} missing values still remain")

print("\n=== DATASETS READY FOR MODELING ===")
print("Available datasets:")
print("1. df_engineered - Original engineered dataset")
print("2. df_standardized - Standardized (Z-score normalized) dataset")
print("3. df_normalized - Normalized (Min-Max scaled) dataset")

# Export processed datasets (optional)
print("\nExporting processed datasets...")

try:
    df_engineered.to_csv('engineered_data.csv', index=False)
    print("✅ Engineered dataset exported to 'engineered_data.csv'")
    
    if 'df_standardized' in locals():
        df_standardized.to_csv('standardized_data.csv', index=False)
        print("✅ Standardized dataset exported to 'standardized_data.csv'")
    
    if 'df_normalized' in locals():
        df_normalized.to_csv('normalized_data.csv', index=False)
        print("✅ Normalized dataset exported to 'normalized_data.csv'")
        
    print("\nAll datasets have been successfully exported!")
except Exception as e:
    print(f"\n⚠️  Error exporting datasets: {e}")

# Final recommendations for modeling
print("\n=== MODELING RECOMMENDATIONS ===")
print("Based on the analysis, here are recommendations for next steps:")
print("\n1. Choose appropriate dataset based on your ML algorithm:")
print("   - Use standardized_data.csv for algorithms sensitive to feature scales (SVM, KNN, PCA)")
print("   - Use normalized_data.csv for neural networks and algorithms requiring bounded inputs")
print("   - Use engineered_data.csv for tree-based algorithms (Random Forest, XGBoost)")

print("\n2. Consider feature selection techniques:")
print("   - Check correlation matrix for multicollinearity")
print("   - Use feature importance from tree-based models")
print("   - Apply dimensionality reduction (PCA) if needed")

print("\n3. Next modeling steps:")
print("   - Split data into training and testing sets")
print("   - Train baseline models")
print("   - Evaluate and tune model performance")
print("   - Consider ensemble methods for improved accuracy")

print("\n🎉 Data analysis and feature engineering pipeline completed successfully!")