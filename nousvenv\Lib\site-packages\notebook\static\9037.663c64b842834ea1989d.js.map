{"version": 3, "file": "9037.663c64b842834ea1989d.js?v=663c64b842834ea1989d", "mappings": ";;;;;;;;;;;AAA2C;;AAEpC,eAAe,oEAAU;AAChC;AACA;AACA,OAAO,iCAAiC;AACxC;AACA,OAAO,+CAA+C;AACtD,OAAO,kEAAkE;AACzE,OAAO,gDAAgD;AACvD;AACA,OAAO,0FAA0F;AACjG,SAAS,gBAAgB;AACzB;AACA,OAAO,iHAAiH;AACxH;AACA,OAAO,iFAAiF;AACxF;AACA,OAAO,uDAAuD;AAC9D;AACA,OAAO,yEAAyE;AAChF;AACA,OAAO,oEAAoE;AAC3E,6BAA6B,cAAc;AAC3C,OAAO,YAAY,kEAAkE,GAAG,6BAA6B;AACrH;AACA,OAAO,mDAAmD;AAC1D,OAAO,8CAA8C;AACrD;AACA,OAAO,sDAAsD;AAC7D;AACA,OAAO,gCAAgC;AACvC,OAAO;AACP;AACA;AACA,OAAO,SAAS,mCAAmC;AACnD,OAAO,2BAA2B;AAClC,OAAO;AACP;AACA;AACA,OAAO,0DAA0D;AACjE,OAAO;AACP;AACA;AACA,OAAO,UAAU,mCAAmC;AACpD,OAAO;AACP;AACA;AACA,OAAO,4DAA4D;AACnE,OAAO;AACP;AACA;AACA,OAAO,6CAA6C;AACpD,OAAO,8BAA8B;AACrC,OAAO,4BAA4B;AACnC,OAAO;AACP;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,GAAG;;;;;;;;;;;AC9DI;AACP;AACA,kBAAkB,kCAAkC;AACpD;AACA;AACA,oBAAoB,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA,eAAe;AACf;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,gBAAgB;AAClC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,oBAAoB;AAC9C;AACA,kCAAkC,2CAA2C;AAC7E;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB,sBAAsB,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/factor.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/simple-mode.js"], "sourcesContent": ["import {simpleMode} from \"./simple-mode.js\"\n\nexport const factor = simpleMode({\n    start: [\n      // comments\n      {regex: /#?!.*/, token: \"comment\"},\n      // strings \"\"\", multiline --> state\n      {regex: /\"\"\"/, token: \"string\", next: \"string3\"},\n      {regex: /(STRING:)(\\s)/, token: [\"keyword\", null], next: \"string2\"},\n      {regex: /\\S*?\"/, token: \"string\", next: \"string\"},\n      // numbers: dec, hex, unicode, bin, fractional, complex\n      {regex: /(?:0x[\\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\\-?\\d+.?\\d*)(?=\\s)/, token: \"number\"},\n      //{regex: /[+-]?/} //fractional\n      // definition: defining word, defined word, etc\n      {regex: /((?:GENERIC)|\\:?\\:)(\\s+)(\\S+)(\\s+)(\\()/, token: [\"keyword\", null, \"def\", null, \"bracket\"], next: \"stack\"},\n      // method definition: defining word, type, defined word, etc\n      {regex: /(M\\:)(\\s+)(\\S+)(\\s+)(\\S+)/, token: [\"keyword\", null, \"def\", null, \"tag\"]},\n      // vocabulary using --> state\n      {regex: /USING\\:/, token: \"keyword\", next: \"vocabulary\"},\n      // vocabulary definition/use\n      {regex: /(USE\\:|IN\\:)(\\s+)(\\S+)(?=\\s|$)/, token: [\"keyword\", null, \"tag\"]},\n      // definition: a defining word, defined word\n      {regex: /(\\S+\\:)(\\s+)(\\S+)(?=\\s|$)/, token: [\"keyword\", null, \"def\"]},\n      // \"keywords\", incl. ; t f . [ ] { } defining words\n      {regex: /(?:;|\\\\|t|f|if|loop|while|until|do|PRIVATE>|<PRIVATE|\\.|\\S*\\[|\\]|\\S*\\{|\\})(?=\\s|$)/, token: \"keyword\"},\n      // <constructors> and the like\n      {regex: /\\S+[\\)>\\.\\*\\?]+(?=\\s|$)/, token: \"builtin\"},\n      {regex: /[\\)><]+\\S+(?=\\s|$)/, token: \"builtin\"},\n      // operators\n      {regex: /(?:[\\+\\-\\=\\/\\*<>])(?=\\s|$)/, token: \"keyword\"},\n      // any id (?)\n      {regex: /\\S+/, token: \"variable\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    vocabulary: [\n      {regex: /;/, token: \"keyword\", next: \"start\"},\n      {regex: /\\S+/, token: \"tag\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    string: [\n      {regex: /(?:[^\\\\]|\\\\.)*?\"/, token: \"string\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    string2: [\n      {regex: /^;/, token: \"keyword\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    string3: [\n      {regex: /(?:[^\\\\]|\\\\.)*?\"\"\"/, token: \"string\", next: \"start\"},\n      {regex: /.*/, token: \"string\"}\n    ],\n    stack: [\n      {regex: /\\)/, token: \"bracket\", next: \"start\"},\n      {regex: /--/, token: \"bracket\"},\n      {regex: /\\S+/, token: \"meta\"},\n      {regex: /\\s+|./, token: null}\n    ],\n    languageData: {\n      name: \"factor\",\n      dontIndentStates: [\"start\", \"vocabulary\", \"string\", \"string3\", \"stack\"],\n      commentTokens: {line: \"!\"}\n    }\n  });\n", "export function simpleMode(states) {\n  ensureState(states, \"start\");\n  var states_ = {}, meta = states.languageData || {}, hasIndentation = false;\n  for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n    var list = states_[state] = [], orig = states[state];\n    for (var i = 0; i < orig.length; i++) {\n      var data = orig[i];\n      list.push(new Rule(data, states));\n      if (data.indent || data.dedent) hasIndentation = true;\n    }\n  }\n  return {\n    name: meta.name,\n    startState: function() {\n      return {state: \"start\", pending: null, indent: hasIndentation ? [] : null};\n    },\n    copyState: function(state) {\n      var s = {state: state.state, pending: state.pending, indent: state.indent && state.indent.slice(0)};\n      if (state.stack)\n        s.stack = state.stack.slice(0);\n      return s;\n    },\n    token: tokenFunction(states_),\n    indent: indentFunction(states_, meta),\n    mergeTokens: meta.mergeTokens,\n    languageData: meta\n  }\n};\n\nfunction ensureState(states, name) {\n  if (!states.hasOwnProperty(name))\n    throw new Error(\"Undefined state \" + name + \" in simple mode\");\n}\n\nfunction toRegex(val, caret) {\n  if (!val) return /(?:)/;\n  var flags = \"\";\n  if (val instanceof RegExp) {\n    if (val.ignoreCase) flags = \"i\";\n    val = val.source;\n  } else {\n    val = String(val);\n  }\n  return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n}\n\nfunction asToken(val) {\n  if (!val) return null;\n  if (val.apply) return val\n  if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n  var result = [];\n  for (var i = 0; i < val.length; i++)\n    result.push(val[i] && val[i].replace(/\\./g, \" \"));\n  return result;\n}\n\nfunction Rule(data, states) {\n  if (data.next || data.push) ensureState(states, data.next || data.push);\n  this.regex = toRegex(data.regex);\n  this.token = asToken(data.token);\n  this.data = data;\n}\n\nfunction tokenFunction(states) {\n  return function(stream, state) {\n    if (state.pending) {\n      var pend = state.pending.shift();\n      if (state.pending.length == 0) state.pending = null;\n      stream.pos += pend.text.length;\n      return pend.token;\n    }\n\n    var curState = states[state.state];\n    for (var i = 0; i < curState.length; i++) {\n      var rule = curState[i];\n      var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n      if (matches) {\n        if (rule.data.next) {\n          state.state = rule.data.next;\n        } else if (rule.data.push) {\n          (state.stack || (state.stack = [])).push(state.state);\n          state.state = rule.data.push;\n        } else if (rule.data.pop && state.stack && state.stack.length) {\n          state.state = state.stack.pop();\n        }\n\n        if (rule.data.indent)\n          state.indent.push(stream.indentation() + stream.indentUnit);\n        if (rule.data.dedent)\n          state.indent.pop();\n        var token = rule.token\n        if (token && token.apply) token = token(matches)\n        if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n          state.pending = [];\n          for (var j = 2; j < matches.length; j++)\n            if (matches[j])\n              state.pending.push({text: matches[j], token: rule.token[j - 1]});\n          stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n          return token[0];\n        } else if (token && token.join) {\n          return token[0];\n        } else {\n          return token;\n        }\n      }\n    }\n    stream.next();\n    return null;\n  };\n}\n\nfunction indentFunction(states, meta) {\n  return function(state, textAfter) {\n    if (state.indent == null || meta.dontIndentStates && meta.dontIndentStates.indexOf(state.state) > -1)\n      return null\n\n    var pos = state.indent.length - 1, rules = states[state.state];\n    scan: for (;;) {\n      for (var i = 0; i < rules.length; i++) {\n        var rule = rules[i];\n        if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n          var m = rule.regex.exec(textAfter);\n          if (m && m[0]) {\n            pos--;\n            if (rule.next || rule.push) rules = states[rule.next || rule.push];\n            textAfter = textAfter.slice(m[0].length);\n            continue scan;\n          }\n        }\n      }\n      break;\n    }\n    return pos < 0 ? 0 : state.indent[pos];\n  };\n}\n"], "names": [], "sourceRoot": ""}