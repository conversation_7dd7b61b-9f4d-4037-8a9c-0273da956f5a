{"version": 3, "file": "5249.47203d8dad661b809e38.js?v=47203d8dad661b809e38", "mappings": ";;;;;;;;;;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,6DAA6D;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;;AAEO;AACP;;AAEA;AACA,YAAY;AACZ;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,wBAAwB;AAClD,mBAAmB;AACnB;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/sparql.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([\"str\", \"lang\", \"langmatches\", \"datatype\", \"bound\", \"sameterm\", \"isiri\", \"isuri\",\n                      \"iri\", \"uri\", \"bnode\", \"count\", \"sum\", \"min\", \"max\", \"avg\", \"sample\",\n                      \"group_concat\", \"rand\", \"abs\", \"ceil\", \"floor\", \"round\", \"concat\", \"substr\", \"strlen\",\n                      \"replace\", \"ucase\", \"lcase\", \"encode_for_uri\", \"contains\", \"strstarts\", \"strends\",\n                      \"strbefore\", \"strafter\", \"year\", \"month\", \"day\", \"hours\", \"minutes\", \"seconds\",\n                      \"timezone\", \"tz\", \"now\", \"uuid\", \"struuid\", \"md5\", \"sha1\", \"sha256\", \"sha384\",\n                      \"sha512\", \"coalesce\", \"if\", \"strlang\", \"strdt\", \"isnumeric\", \"regex\", \"exists\",\n                      \"isblank\", \"isliteral\", \"a\", \"bind\"]);\nvar keywords = wordRegexp([\"base\", \"prefix\", \"select\", \"distinct\", \"reduced\", \"construct\", \"describe\",\n                           \"ask\", \"from\", \"named\", \"where\", \"order\", \"limit\", \"offset\", \"filter\", \"optional\",\n                           \"graph\", \"by\", \"asc\", \"desc\", \"as\", \"having\", \"undef\", \"values\", \"group\",\n                           \"minus\", \"in\", \"not\", \"service\", \"silent\", \"using\", \"insert\", \"delete\", \"union\",\n                           \"true\", \"false\", \"with\",\n                           \"data\", \"copy\", \"to\", \"move\", \"add\", \"create\", \"drop\", \"clear\", \"load\", \"into\"]);\nvar operatorChars = /[*+\\-<>=&|\\^\\/!\\?]/;\nvar PN_CHARS = \"[A-Za-z_\\\\-0-9]\";\nvar PREFIX_START = new RegExp(\"[A-Za-z]\");\nvar PREFIX_REMAINDER = new RegExp(\"((\" + PN_CHARS + \"|\\\\.)*(\" + PN_CHARS + \"))?:\");\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"$\" || ch == \"?\") {\n    if(ch == \"?\" && stream.match(/\\s/, false)){\n      return \"operator\";\n    }\n    stream.match(/^[A-Za-z0-9_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][A-Za-z0-9_\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]*/);\n    return \"variableName.local\";\n  }\n  else if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"bracket\";\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    return \"operator\";\n  }\n  else if (ch == \":\") {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  else if (ch == \"@\") {\n    stream.eatWhile(/[a-z\\d\\-]/i);\n    return \"meta\";\n  }\n  else if (PREFIX_START.test(ch) && stream.match(PREFIX_REMAINDER)) {\n    eatPnLocal(stream);\n    return \"atom\";\n  }\n  stream.eatWhile(/[_\\w\\d]/);\n  var word = stream.current();\n  if (ops.test(word))\n    return \"builtin\";\n  else if (keywords.test(word))\n    return \"keyword\";\n  else\n    return \"variable\";\n}\n\nfunction eatPnLocal(stream) {\n  stream.match(/(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/i);\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const sparql = {\n  name: \"sparql\",\n\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) {\n        popContext(state);\n        if (curPunc == \"}\" && state.context && state.context.type == \"pattern\")\n          popContext(state);\n      }\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n\n"], "names": [], "sourceRoot": ""}