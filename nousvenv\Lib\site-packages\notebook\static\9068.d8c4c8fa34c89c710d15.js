"use strict";
(self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] = self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] || []).push([[9068],{

/***/ 59068:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  jupyterIcon: () => (/* reexport */ jupyterIcon)
});

// EXTERNAL MODULE: consume shared module (default) @jupyterlab/ui-components@~4.4.5 (singleton) (fallback: ../node_modules/@jupyterlab/ui-components/lib/index.js)
var index_js_ = __webpack_require__(52104);
;// CONCATENATED MODULE: ../packages/ui-components/style/icons/jupyter.svg
const jupyter_namespaceObject = "<svg width=\"189\" height=\"51\" version=\"2.0\" viewBox=\"0 0 189 51\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <title>Jupyter</title>\n <g class=\"jp-icon3\" transform=\"translate(-1638 -2093)\" fill=\"#616161\">\n  <g style=\"mix-blend-mode:normal\">\n   <g style=\"mix-blend-mode:normal\">\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1688.9 2106.2)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path0_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1705.4 2106.2)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path1_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1730.2 2105.7)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path2_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1752.9 2106.2)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path3_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1775.8 2100)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path4_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1791.8 2105.7)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path5_fill\"/>\n      </g>\n     </g>\n     <g style=\"mix-blend-mode:normal\">\n      <g style=\"mix-blend-mode:normal\">\n       <use transform=\"translate(1815.8 2105.7)\" style=\"mix-blend-mode:normal\" xlink:href=\"#path6_fill\"/>\n      </g>\n     </g>\n    </g>\n   </g>\n   <g style=\"mix-blend-mode:normal\">\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <use transform=\"translate(1669.3 2093.3)\" fill=\"#767677\" style=\"mix-blend-mode:normal\" xlink:href=\"#path7_fill\"/>\n     </g>\n    </g>\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <use transform=\"translate(1639.7 2124)\" fill=\"#F37726\" style=\"mix-blend-mode:normal\" xlink:href=\"#path8_fill\"/>\n     </g>\n    </g>\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <use transform=\"translate(1639.7 2097.5)\" fill=\"#F37726\" style=\"mix-blend-mode:normal\" xlink:href=\"#path9_fill\"/>\n     </g>\n    </g>\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <use transform=\"translate(1639.8 2135.8)\" fill=\"#989798\" style=\"mix-blend-mode:normal\" xlink:href=\"#path10_fill\"/>\n     </g>\n    </g>\n    <g style=\"mix-blend-mode:normal\">\n     <g style=\"mix-blend-mode:normal\">\n      <use transform=\"translate(1638.4 2098.1)\" fill=\"#6F7070\" style=\"mix-blend-mode:normal\" xlink:href=\"#path11_fill\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n </g>\n <defs>\n  <path id=\"path0_fill\" d=\"m5.6259 17.928c0 5.1461-0.3925 6.8263-1.4392 8.0666-1.1426 1.0559-2.637 1.6399-4.1867 1.6362l0.39251 3.0612c2.402 0.0333 4.7305-0.8352 6.5331-2.4366 1.8753-1.9529 2.5295-4.6535 2.5295-8.7967v-19.458h-3.8378v17.954l0.00872-0.0264z\"/>\n  <path id=\"path1_fill\" d=\"m17.741 15.623c0 2.2168 0 4.1696 0.1744 5.8498h-3.3581l-0.218-3.5187h-0.0873c-0.7069 1.2298-1.7261 2.2473-2.9526 2.9477-1.2265 0.7005-2.6159 1.0585-4.0252 1.0372-3.3145 0-7.2744-1.8649-7.2744-9.3948v-12.544h3.8378v11.902c0 4.0817 1.2211 6.8262 4.7014 6.8262 1.0917-0.0201 2.1533-0.3647 3.0516-0.9907 0.8984-0.6259 1.5936-1.5053 1.9986-2.5279 0.2328-0.6395 0.351-1.3157 0.3489-1.9969v-13.195h3.8379v15.605h-0.0349z\"/>\n  <path id=\"path2_fill\" d=\"m0.17445 7.5363c0-2.7446-0.087223-4.9613-0.17445-7.0374h3.4453l0.17445 3.677h0.08722c0.75374-1.3174 1.85-2.4022 3.1705-3.137 1.3205-0.73485 2.8149-1.0919 4.322-1.0326 5.0939 0 8.9317 4.3983 8.9317 10.908 0 7.7147-4.6141 11.524-9.5946 11.524-1.2785 0.0541-2.5489-0.2281-3.6867-0.8187-1.1377-0.5907-2.1036-1.4696-2.8028-2.5504h-0.08723v11.656h-3.7942v-23.223l0.008722 0.03519zm3.7942 5.7179c0.01001 0.5349 0.0684 1.0679 0.17444 1.5922 0.31262 1.3003 1.0491 2.4571 2.0914 3.2849 1.0423 0.8279 2.33 1.2788 3.6567 1.2805 4.0559 0 6.4022-3.3691 6.4022-8.2864 0-4.3016-2.2242-7.9786-6.2714-7.9786-1.3611 0.03841-2.6704 0.53457-3.7198 1.4096-1.0494 0.87505-1.7786 2.0788-2.0718 3.4198-0.15373 0.51742-0.24166 1.0524-0.26167 1.5922v3.6858z\"/>\n  <path id=\"path3_fill\" d=\"m4.1606 0 4.6141 12.676c0.47973 1.4163 1.0031 3.1053 1.352 4.3984h0.0872c0.3925-1.2843 0.8286-2.9293 1.352-4.4775l4.1867-12.588h4.0559l-5.7481 15.297c-2.7475 7.3541-4.6141 11.128-7.2308 13.433-1.3222 1.2403-2.9429 2.1106-4.7014 2.5246l-0.95946-3.2811c1.2303-0.4134 2.3704-1.0615 3.3581-1.9089 1.3957-1.1762 2.5006-2.664 3.2273-4.3456 0.15514-0.2904 0.25848-0.606 0.30528-0.9324-0.03445-0.3518-0.12272-0.696-0.26167-1.0205l-7.7978-19.766h4.1867l-0.02616-0.0087967z\"/>\n  <path id=\"path4_fill\" d=\"m7.0215 0v6.1577h5.4864v2.9733h-5.4864v11.559c0 2.639 0.7414 4.1696 2.8784 4.1696 0.74972 0.0118 1.4976-0.077 2.2242-0.2639l0.1744 2.9293c-1.0915 0.3877-2.2451 0.5667-3.4017 0.5278-0.76701 0.0474-1.535-0.0744-2.2506-0.357-0.71554-0.2826-1.3614-0.7191-1.8925-1.2792-1.0903-1.1523-1.4828-3.0612-1.4828-5.5858v-11.7h-3.2709v-2.9733h3.2709v-5.1461l3.7506-1.0116z\"/>\n  <path id=\"path5_fill\" d=\"m3.6285 11.928c0.08723 5.278 3.4017 7.4508 7.2308 7.4508 2.0019 0.0634 3.9934-0.3149 5.8353-1.1084l0.6542 2.7886c-2.2069 0.9347-4.585 1.3874-6.9779 1.3283-6.4894 0-10.371-4.3456-10.371-10.82 0-6.4743 3.7506-11.568 9.8912-11.568 6.8819 0 8.7223 6.1577 8.7223 10.107-0.0065 0.6091-0.0501 1.2172-0.1308 1.8209h-14.854zm11.243-2.7885c0.0436-2.4807-1.003-6.3424-5.3119-6.3424-3.8814 0-5.5736 3.633-5.8789 6.3424h11.199-0.0087z\"/>\n  <path id=\"path6_fill\" d=\"m0.17445 7.1785c0-2.5246-0.043612-4.6974-0.17445-6.6943h3.3581l0.13083 4.2136h0.17445c0.95946-2.8853 3.2709-4.6974 5.8353-4.6974 0.36765-0.0054214 0.73435 0.038958 1.0902 0.13195v3.677c-0.4296-0.09447-0.86861-0.13874-1.3083-0.13195-2.7039 0-4.6141 2.0848-5.1375 5.0053-0.10796 0.60107-0.16631 1.2101-0.17445 1.8209v11.436h-3.7942v-14.761z\"/>\n  <path id=\"path7_fill\" d=\"m5.8935 2.844c0.02536 0.58765-0.12268 1.1697-0.42538 1.6724-0.3027 0.50277-0.74647 0.9037-1.2752 1.1521-0.52869 0.24837-1.1186 0.333-1.6949 0.2432-0.57639-0.08981-1.1134-0.35001-1.5432-0.7477-0.42973-0.39768-0.73284-0.91498-0.87099-1.4864-0.13815-0.57146-0.10513-1.1714 0.094877-1.7239 0.20001-0.55254 0.55803-1.0328 1.0288-1.3801 0.47072-0.34728 1.033-0.54595 1.6157-0.57087 0.78063-0.033384 1.5425 0.24712 2.1182 0.77988 0.57569 0.53276 0.91814 1.2742 0.95211 2.0614z\"/>\n  <path id=\"path8_fill\" d=\"m18.265 7.1341c-7.8501 0-14.706-2.8765-18.265-7.1341 1.3254 3.8204 3.7956 7.1308 7.0686 9.473 3.2731 2.3422 7.1871 3.6004 11.2 3.6004s7.9273-1.2582 11.2-3.6004c3.273-2.3422 5.7432-5.6526 7.0686-9.473-3.5675 4.2576-10.423 7.1341-18.273 7.1341z\"/>\n  <path id=\"path9_fill\" d=\"m18.273 5.9393c7.8502 0 14.706 2.8765 18.265 7.1341-1.3254-3.8204-3.7956-7.1308-7.0686-9.473-3.2731-2.3422-7.1871-3.6004-11.2-3.6004s-7.9273 1.2582-11.2 3.6004c-3.273 2.3422-5.7432 5.6526-7.0686 9.473 3.5674-4.2488 10.423-7.1341 18.273-7.1341z\"/>\n  <path id=\"path10_fill\" d=\"m7.4279 3.5834c0.03219 0.74092-0.15434 1.4748-0.53596 2.1088s-0.94118 1.1394-1.6078 1.4525c-0.66664 0.31303-1.4104 0.41954-2.1371 0.30603s-1.4037-0.44193-1.9452-0.94368c-0.54151-0.50175-0.92323-1.1543-1.0968-1.8749-0.17359-0.72067-0.13125-1.4771 0.12166-2.1735 0.25291-0.69639 0.70502-1.3014 1.2991-1.7385 0.59407-0.4371 1.3034-0.68659 2.0381-0.7169 0.98278-0.040537 1.9414 0.31357 2.6657 0.9847 0.72432 0.67112 1.1552 1.6045 1.1983 2.5955z\"/>\n  <path id=\"path11_fill\" d=\"m2.2747 4.3963c-0.43108 0.01879-0.858-0.09184-1.2267-0.31786-0.36872-0.22603-0.66266-0.55729-0.84462-0.95187s-0.24375-0.83473-0.17756-1.2648c0.066192-0.43001 0.25739-0.83055 0.5494-1.1509 0.29201-0.32037 0.6717-0.54618 1.091-0.64882 0.41931-0.10265 0.85939-0.077531 1.2645 0.072176 0.40515 0.14971 0.75716 0.41727 1.0115 0.76882 0.2543 0.35156 0.39947 0.7713 0.41713 1.2061 0.02364 0.58191-0.18257 1.1495-0.57338 1.5783-0.39081 0.42878-0.93431 0.6837-1.5113 0.70883z\"/>\n </defs>\n</svg>\n";
;// CONCATENATED MODULE: ../packages/ui-components/lib/icon/iconimports.js
/*-----------------------------------------------------------------------------
| Copyright (c) Jupyter Development Team.
| Distributed under the terms of the Modified BSD License.
|----------------------------------------------------------------------------*/


const jupyterIcon = new index_js_.LabIcon({
    name: 'notebook-ui-components:jupyter',
    svgstr: jupyter_namespaceObject,
});

;// CONCATENATED MODULE: ../packages/ui-components/lib/icon/index.js
// Copyright (c) Jupyter Development Team.
// Distributed under the terms of the Modified BSD License.


;// CONCATENATED MODULE: ../packages/ui-components/lib/index.js
// Copyright (c) Jupyter Development Team.
// Distributed under the terms of the Modified BSD License.



/***/ })

}]);
//# sourceMappingURL=9068.d8c4c8fa34c89c710d15.js.map?v=d8c4c8fa34c89c710d15