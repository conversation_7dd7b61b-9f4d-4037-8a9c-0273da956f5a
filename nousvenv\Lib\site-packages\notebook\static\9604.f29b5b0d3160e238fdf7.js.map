{"version": 3, "file": "9604.f29b5b0d3160e238fdf7.js?v=f29b5b0d3160e238fdf7", "mappings": ";;;;;;;;;;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,MAAM;AACN;;AAEA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,0BAA0B,wBAAwB;AAClD,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,cAAc;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/turtle.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([]);\nvar keywords = wordRegexp([\"@prefix\", \"@base\", \"a\"]);\nvar operatorChars = /[*+\\-<>=&|]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  }\n  else if (ch == \":\") {\n    return \"operator\";\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if(stream.peek() == \":\") {\n      return \"variableName.special\";\n    } else {\n      var word = stream.current();\n\n      if(keywords.test(word)) {\n        return \"meta\";\n      }\n\n      if(ch >= \"A\" && ch <= \"Z\") {\n        return \"comment\";\n      } else {\n        return \"keyword\";\n      }\n    }\n    var word = stream.current();\n    if (ops.test(word))\n      return null;\n    else if (keywords.test(word))\n      return \"meta\";\n    else\n      return \"variable\";\n  }\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const turtle = {\n  name: \"turtle\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) popContext(state);\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}